import { useQuery } from '@tanstack/react-query';
import { useDependencyInjection } from '../../../core/hooks/use-dependency-injection';
import { IGetFacilities } from '../../business/api/facility.service';
import { FACILITY_DEPENDENCY_INJECTION_TYPES } from '../../business/types/facility-symbols';
import { facilityContainer } from '../../integration/register-dependency-injections';

export const useFacilityList = (search: string) => {
  const getFacilities: IGetFacilities = useDependencyInjection(
    facilityContainer,
    FACILITY_DEPENDENCY_INJECTION_TYPES.GetFacilities
  );

  return useQuery({
    queryKey: ['facilities', search],
    queryFn: () => getFacilities.execute(search),
  });
};
