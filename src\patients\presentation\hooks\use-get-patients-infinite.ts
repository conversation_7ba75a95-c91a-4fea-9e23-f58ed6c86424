import { useInfiniteQuery } from '@tanstack/react-query';
import { useDependencyInjection } from '../../../core/hooks/use-dependency-injection';
import { IGetPatients } from '../../business/api/patient.service';
import { PatientListParams } from '../../business/types/patient-list-params';
import { IPatient } from '../../business/types/patient-response';
import { PATIENT_SYMBOLS } from '../../business/types/patient-symbols';
import { patientContainer } from '../../integration/register-dependency-injection';

interface UseGetPatientsInfiniteParams {
  facilityId?: string;
  search?: string;
  limit?: number;
}

interface PatientsPage {
  patients: IPatient[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_count: number;
  };
}

export const useGetPatientsInfinite = (params: UseGetPatientsInfiniteParams) => {
  const getPatients: IGetPatients = useDependencyInjection(
    patientContainer,
    PATIENT_SYMBOLS.GetPatients
  );

  const buildFilters = (pageParam: number) => {
    const filters: PatientListParams['filters'] = [
      {
        field: 'page',
        value: pageParam,
      },
      {
        field: 'limit',
        value: params.limit || 20,
      },
    ];

    if (params.facilityId) {
      filters.push({
        field: 'facility_id',
        value: params.facilityId,
      });
    }

    if (params.search && params.search.trim()) {
      filters.push({
        field: 'name',
        value: params.search.trim(),
      });
    }

    return filters;
  };

  return useInfiniteQuery<PatientsPage, Error>({
    queryKey: ['patients-infinite', params.facilityId, params.search, params.limit],
    queryFn: async ({ pageParam = 1 }) => {
      const filters = buildFilters(pageParam as number);
      const result = await getPatients.execute({ filters });
      return {
        patients: result.patients,
        pagination: result.pagination,
      };
    },
    getNextPageParam: (lastPage) => {
      const { current_page, total_pages } = lastPage.pagination;
      return current_page < total_pages ? current_page + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      const { current_page } = firstPage.pagination;
      return current_page > 1 ? current_page - 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (was cacheTime)
  });
};

// Helper to get all patients from all pages
export const getAllPatientsFromPages = (data: PatientsPage[] | undefined): IPatient[] => {
  if (!data) return [];
  return data.flatMap((page) => page.patients);
};

// Helper to get total count
export const getTotalCount = (data: PatientsPage[] | undefined): number => {
  if (!data || data.length === 0) return 0;
  return data[0].pagination.total_count;
};
