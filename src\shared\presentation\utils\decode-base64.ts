/**
 * BLE Data Decoder Function - Based on Official 2301A/B Protocol Documentation
 * Decodes base64 BLE data from your fitness device using the official protocol
 */

// Device constants from official protocol documentation
const DeviceConst = {
  // Official protocol commands
  CMD_SET_TIME: 0x01,
  CMD_SET_PERSONAL_INFO: 0x02,
  C<PERSON>_SET_DEVICE_ID: 0x05,
  CMD_REAL_TIME_STEP: 0x09,
  CMD_FACTORY_RESET: 0x12,
  C<PERSON>_GET_BATTERY: 0x13,
  CMD_SPORTS_MODE: 0x19,
  CMD_MCU_RESET: 0x2e,
  CMD_READ_MAC: 0x22,
  CMD_READ_VERSION: 0x27,
  CMD_HEALTH_MEASUREMENT: 0x28,
  CMD_SET_AUTO_HR: 0x2a,
  CMD_GET_AUTO_HR: 0x2b,
  CMD_SET_DEVICE_NAME: 0x3d,
  CMD_GET_DEVICE_NAME: 0x3e,
  <PERSON><PERSON>_GET_TIME: 0x41,
  C<PERSON>_GET_PERSONAL_INFO: 0x42,
  C<PERSON>_GET_TOTAL_STEPS: 0x51,
  C<PERSON>_GET_STEP_DETAILS: 0x52,
  CMD_GET_SLEEP_DATA: 0x53,
  CMD_GET_HEART_DATA: 0x54,
  CMD_GET_SINGLE_HR: 0x55,
  CMD_GET_HRV_DATA: 0x56,
  CMD_GET_TEMP_DATA: 0x62,
  CMD_GET_BLOOD_OXYGEN: 0x66,

  // Your specific commands (0x86 is real-time data response)
  CMD_REAL_TIME_DATA_RESPONSE: 0x86, // This is what you're receiving!
} as const;

// Type definitions
interface BaseBleData {
  type: string;
  command: number;
  commandName: string;
  rawData: number[];
  hex: string;
  timestamp: string;
}

interface RealTimeData extends BaseBleData {
  type: 'realTimeData';
  steps: number;
  calories: number;
  distance: number;
  movementTime: number;
  rapidMovementTime: number;
  heartRate: number;
  temperature: number;
  bloodOxygen: number;
  isActive: boolean;
}

interface HealthMeasurement extends BaseBleData {
  type: 'healthMeasurement';
  measurementType: number;
  measurementTypeName: string;
  heartRate: number;
  bloodOxygen: number;
  hrv: number;
  fatigueLevel: number;
  highBloodPressure: number;
  lowBloodPressure: number;
}

interface BatteryData extends BaseBleData {
  type: 'battery';
  batteryLevel: number;
  voltage: number;
  temperature: number;
}

interface DeviceTimeData extends BaseBleData {
  type: 'deviceTime';
  year: number;
  month: number;
  day: number;
  hour: number;
  minute: number;
  second: number;
  weekday: number;
  dateString: string;
}

interface SportsMode extends BaseBleData {
  type: 'sportsMode';
  mode: number;
  modeName: string;
  success: boolean;
}

interface SuccessResponse extends BaseBleData {
  type: 'successResponse';
  success: boolean;
}

interface ErrorResponse extends BaseBleData {
  type: 'errorResponse';
  errorCode: number;
}

interface UnknownCommand extends BaseBleData {
  type: 'unknownCommand';
}

interface SingleHeartRateData extends BaseBleData {
  type: 'singleHeartRateData';
  readings: Array<{
    dataId: number;
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    second: number;
    heartRate: number;
    timestamp: string;
  }>;
  totalReadings: number;
  latestHeartRate: number;
}

type BleDecodedData =
  | RealTimeData
  | HealthMeasurement
  | BatteryData
  | DeviceTimeData
  | SportsMode
  | SingleHeartRateData
  | SuccessResponse
  | ErrorResponse
  | UnknownCommand;

// Helper functions
function base64ToByteArray(base64: string): number[] {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return Array.from(bytes);
}

function byteArrayToHex(bytes: number[]): string {
  return bytes
    .map((byte) => byte.toString(16).padStart(2, '0'))
    .join('')
    .toUpperCase();
}

function getCommandName(cmdByte: number): string {
  const commandNames: Record<number, string> = {
    [DeviceConst.CMD_SET_TIME]: 'Set Time',
    [DeviceConst.CMD_SET_PERSONAL_INFO]: 'Set Personal Info',
    [DeviceConst.CMD_SET_DEVICE_ID]: 'Set Device ID',
    [DeviceConst.CMD_REAL_TIME_STEP]: 'Real-time Step Mode',
    [DeviceConst.CMD_FACTORY_RESET]: 'Factory Reset',
    [DeviceConst.CMD_GET_BATTERY]: 'Get Battery Level',
    [DeviceConst.CMD_SPORTS_MODE]: 'Sports Mode Control',
    [DeviceConst.CMD_MCU_RESET]: 'MCU Reset',
    [DeviceConst.CMD_READ_MAC]: 'Read MAC Address',
    [DeviceConst.CMD_READ_VERSION]: 'Read Version',
    [DeviceConst.CMD_HEALTH_MEASUREMENT]: 'Health Measurement',
    [DeviceConst.CMD_SET_AUTO_HR]: 'Set Auto Heart Rate',
    [DeviceConst.CMD_GET_AUTO_HR]: 'Get Auto Heart Rate',
    [DeviceConst.CMD_SET_DEVICE_NAME]: 'Set Device Name',
    [DeviceConst.CMD_GET_DEVICE_NAME]: 'Get Device Name',
    [DeviceConst.CMD_GET_TIME]: 'Get Time',
    [DeviceConst.CMD_GET_PERSONAL_INFO]: 'Get Personal Info',
    [DeviceConst.CMD_GET_TOTAL_STEPS]: 'Get Total Steps',
    [DeviceConst.CMD_GET_STEP_DETAILS]: 'Get Step Details',
    [DeviceConst.CMD_GET_SLEEP_DATA]: 'Get Sleep Data',
    [DeviceConst.CMD_GET_HEART_DATA]: 'Get Heart Rate Data',
    [DeviceConst.CMD_GET_SINGLE_HR]: 'Get Single Heart Rate',
    [DeviceConst.CMD_GET_HRV_DATA]: 'Get HRV Data',
    [DeviceConst.CMD_GET_TEMP_DATA]: 'Get Temperature Data',
    [DeviceConst.CMD_GET_BLOOD_OXYGEN]: 'Get Blood Oxygen Data',
    [DeviceConst.CMD_REAL_TIME_DATA_RESPONSE]: 'Real-time Data Response',
  };

  return commandNames[cmdByte] || `Unknown Command (0x${cmdByte.toString(16).padStart(2, '0')})`;
}

// Parse real-time data response (0x86) - this is what you're receiving!
function parseRealTimeData(data: number[]): RealTimeData {
  // Your device sends different sub-commands in byte 1:
  // 0x01: Basic real-time data (all zeros when idle)
  // 0x02: Active measurement data

  const subCommand = data[1];

  if (subCommand === 0x02) {
    // Active measurement data format
    // Based on your log: 86 02 01 01 02 00 00 00 00 00 00 00 00 00 00 8C
    const param1 = data[2]; // 0x01
    const param2 = data[3]; // 0x01
    const param3 = data[4]; // 0x02

    return {
      type: 'realTimeData',
      command: data[0],
      commandName: getCommandName(data[0]),
      steps: 0, // Would need to determine correct mapping
      calories: 0,
      distance: 0,
      movementTime: 0,
      rapidMovementTime: 0,
      heartRate: param1, // Possibly heart rate
      temperature: param2, // Possibly temperature
      bloodOxygen: param3, // Possibly blood oxygen
      isActive: true,
      rawData: data,
      hex: byteArrayToHex(data),
      timestamp: new Date().toISOString(),
    };
  } else {
    // Standard real-time data format (subCommand 0x01)
    // According to protocol documentation for real-time step counting (0x09 response):
    const steps = (data[2] << 24) | (data[3] << 16) | (data[4] << 8) | data[5];
    const calories = ((data[6] << 24) | (data[7] << 16) | (data[8] << 8) | data[9]) / 100;
    const distance = ((data[10] << 24) | (data[11] << 16) | (data[12] << 8) | data[13]) / 100;

    const isActive = !data.slice(2, 15).every((b) => b === 0);

    return {
      type: 'realTimeData',
      command: data[0],
      commandName: getCommandName(data[0]),
      steps: steps,
      calories: calories,
      distance: distance,
      movementTime: 0,
      rapidMovementTime: 0,
      heartRate: 0,
      temperature: 0,
      bloodOxygen: 0,
      isActive: isActive,
      rawData: data,
      hex: byteArrayToHex(data),
      timestamp: new Date().toISOString(),
    };
  }
}

// Parse health measurement data (0x28 response)
function parseHealthMeasurement(data: number[]): HealthMeasurement {
  const measurementType = data[1];
  const heartRate = data[2];
  const bloodOxygen = data[3];
  const hrv = data[4];
  const fatigueLevel = data[5];
  const highBloodPressure = data[6];
  const lowBloodPressure = data[7];

  const typeNames: Record<number, string> = {
    1: 'HRV Measurement',
    2: 'Heart Rate Measurement',
    3: 'Blood Oxygen Measurement',
  };

  return {
    type: 'healthMeasurement',
    command: data[0],
    commandName: getCommandName(data[0]),
    measurementType: measurementType,
    measurementTypeName: typeNames[measurementType] || 'Unknown',
    heartRate: heartRate,
    bloodOxygen: bloodOxygen,
    hrv: hrv,
    fatigueLevel: fatigueLevel,
    highBloodPressure: highBloodPressure,
    lowBloodPressure: lowBloodPressure,
    rawData: data,
    hex: byteArrayToHex(data),
    timestamp: new Date().toISOString(),
  };
}

// Parse battery data (0x13 response)
function parseBatteryData(data: number[]): BatteryData {
  const batteryLevel = data[1]; // 0-100 percentage
  const voltage = (data[2] << 8) | data[3]; // Voltage value
  const temperature = (data[4] << 8) | data[5]; // Temperature value

  return {
    type: 'battery',
    command: data[0],
    commandName: getCommandName(data[0]),
    batteryLevel: batteryLevel,
    voltage: voltage,
    temperature: temperature,
    rawData: data,
    hex: byteArrayToHex(data),
    timestamp: new Date().toISOString(),
  };
}

// Parse device time (0x41 response)
function parseDeviceTime(data: number[]): DeviceTimeData {
  // BCD format: AA BB CC DD EE FF (year, month, day, hour, minute, second)
  const year = 2000 + parseInt(data[1].toString(16)); // BCD to decimal
  const month = parseInt(data[2].toString(16));
  const day = parseInt(data[3].toString(16));
  const hour = parseInt(data[4].toString(16));
  const minute = parseInt(data[5].toString(16));
  const second = parseInt(data[6].toString(16));
  const weekday = data[7];

  return {
    type: 'deviceTime',
    command: data[0],
    commandName: getCommandName(data[0]),
    year: year,
    month: month,
    day: day,
    hour: hour,
    minute: minute,
    second: second,
    weekday: weekday,
    dateString: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`,
    rawData: data,
    hex: byteArrayToHex(data),
    timestamp: new Date().toISOString(),
  };
}

// Parse single heart rate data (0x55 response) - variable length
function parseSingleHeartRateData(data: number[]): BleDecodedData {
  // Variable length response for single heart rate readings
  // Format: Multiple readings of [0x55 ID1 ID2 YY MM DD HH mm SS HR]
  const readings = [];

  let offset = 0;
  while (offset < data.length - 1) {
    // -1 to account for final 0xFF
    if (data[offset] === 0x55 && offset + 9 < data.length) {
      const reading = {
        dataId: (data[offset + 1] << 8) | data[offset + 2],
        year: 2000 + data[offset + 3],
        month: data[offset + 4],
        day: data[offset + 5],
        hour: data[offset + 6],
        minute: data[offset + 7],
        second: data[offset + 8],
        heartRate: data[offset + 9],
        timestamp: new Date(
          2000 + data[offset + 3],
          data[offset + 4] - 1,
          data[offset + 5],
          data[offset + 6],
          data[offset + 7],
          data[offset + 8]
        ).toISOString(),
      };
      readings.push(reading);
      offset += 10; // Move to next reading
    } else {
      offset++;
    }
  }

  return {
    type: 'singleHeartRateData',
    command: 0x55,
    commandName: 'Single Heart Rate Data',
    readings: readings,
    totalReadings: readings.length,
    latestHeartRate: readings.length > 0 ? readings[readings.length - 1].heartRate : 0,
    rawData: data,
    hex: byteArrayToHex(data),
    timestamp: new Date().toISOString(),
  };
}
function parseSportsMode(data: number[]): SportsMode {
  const success = data[1] === 1;

  const modeNames: Record<number, string> = {
    0: 'Run',
    1: 'Cycling',
    2: 'Badminton',
    3: 'Football',
    4: 'Tennis',
    5: 'Yoga',
    6: 'Meditation',
    7: 'Dance',
    8: 'Basketball',
    9: 'Walk',
    10: 'Workout',
    11: 'Cricket',
    12: 'Hiking',
    13: 'Aerobics',
    14: 'Ping-Pong',
    15: 'Rope Jump',
    16: 'Sit-ups',
    17: 'Volleyball',
  };

  return {
    type: 'sportsMode',
    command: data[0],
    commandName: getCommandName(data[0]),
    mode: data[2] || 0,
    modeName: modeNames[data[2]] || 'Unknown Sport',
    success: success,
    rawData: data,
    hex: byteArrayToHex(data),
    timestamp: new Date().toISOString(),
  };
}

// Validate checksum according to protocol
function validateChecksum(data: number[]): boolean {
  if (data.length < 2) return false;

  let crc = 0;
  for (let i = 0; i < data.length - 1; i++) {
    crc += data[i];
  }

  const expectedChecksum = crc & 0xff;
  const actualChecksum = data[data.length - 1];

  return expectedChecksum === actualChecksum;
}

/**
 * Main decode function for BLE data based on official protocol
 * @param base64Value - Base64 encoded value from BLE characteristic
 * @returns Parsed data object according to official protocol
 */
export function decodeBleData(base64Value: string): BleDecodedData | null {
  if (!base64Value) {
    return null;
  }

  try {
    const data = base64ToByteArray(base64Value);
    const hex = byteArrayToHex(data);

    console.log(`Received data: ${hex}`);

    // Validate data length (expecting 16 bytes for most commands, but some are variable)
    if (data.length !== 16) {
      // Handle variable length commands
      if (data[0] === DeviceConst.CMD_GET_SINGLE_HR && data.length > 16) {
        // Single heart rate data can be variable length
        return parseSingleHeartRateData(data);
      }

      console.warn(`Unexpected data length: ${data.length}, expected 16`);

      // Special case for blood sugar data which can be 153 bytes
      if (data.length === 153 && data[0] === DeviceConst.CMD_GET_BLOOD_OXYGEN) {
        // return parseBloodSugarData(data);
      }

      return {
        type: 'unknownCommand',
        command: data[0] || 0,
        commandName: 'Invalid Length',
        rawData: data,
        hex: hex,
        timestamp: new Date().toISOString(),
      };
    }

    // Validate checksum according to protocol
    const checksumValid = validateChecksum(data);
    if (!checksumValid) {
      console.warn('Checksum validation failed');
    }

    const command = data[0];

    // Note: 0x86 is a valid response command, not an error
    // Error responses have the original command OR'd with 0x80 (e.g., 0x01 becomes 0x81)
    // But 0x86 appears to be a legitimate response command for real-time data

    // Parse based on official protocol commands
    switch (command) {
      case DeviceConst.CMD_REAL_TIME_DATA_RESPONSE: // 0x86
        return parseRealTimeData(data);

      case DeviceConst.CMD_HEALTH_MEASUREMENT: // 0x28
        return parseHealthMeasurement(data);
        return parseHealthMeasurement(data);

      case DeviceConst.CMD_GET_BATTERY:
        return parseBatteryData(data);

      case DeviceConst.CMD_GET_TIME:
        return parseDeviceTime(data);

      case DeviceConst.CMD_GET_SINGLE_HR:
        return parseSingleHeartRateData(data);

      case DeviceConst.CMD_SPORTS_MODE:
        return parseSportsMode(data);

      // Success responses for set commands
      case DeviceConst.CMD_SET_TIME:
      case DeviceConst.CMD_SET_PERSONAL_INFO:
      case DeviceConst.CMD_SET_DEVICE_ID:
      case DeviceConst.CMD_SET_AUTO_HR:
      case DeviceConst.CMD_SET_DEVICE_NAME:
      case DeviceConst.CMD_FACTORY_RESET:
      case DeviceConst.CMD_MCU_RESET:
        return {
          type: 'successResponse',
          command: command,
          commandName: getCommandName(command),
          success: true,
          rawData: data,
          hex: hex,
          timestamp: new Date().toISOString(),
        };

      // Handle actual error responses (original command + 0x80)
      case 0x81:
      case 0x82:
      case 0x85:
      case 0x89:
      case 0x92:
      case 0x93:
      case 0xa2:
      case 0xa6:
      case 0xa7:
      case 0xaa:
      case 0xab:
      case 0xae:
      case 0xc1:
      case 0xc2:
        return {
          type: 'errorResponse',
          command: command,
          commandName: `Error Response (0x${command.toString(16).padStart(2, '0')})`,
          errorCode: command,
          rawData: data,
          hex: hex,
          timestamp: new Date().toISOString(),
        };

      default:
        return {
          type: 'unknownCommand',
          command: command,
          commandName: getCommandName(command),
          rawData: data,
          hex: hex,
          timestamp: new Date().toISOString(),
        };
    }
  } catch (error) {
    console.error('Error decoding BLE data:', error);
    return {
      type: 'unknownCommand',
      command: 0,
      commandName: 'Decode Error',
      rawData: [],
      hex: '',
      timestamp: new Date().toISOString(),
    };
  }
}

// BLE Characteristic interface
interface BleCharacteristic {
  value: string | null;
}

interface BleError {
  message: string;
}

/**
 * Handle BLE characteristic callback with official protocol parsing
 */
export function handleBleCharacteristic(
  error: BleError | null,
  characteristic: BleCharacteristic | null
): BleDecodedData | null {
  if (error) {
    console.error(`BLE error: ${error.message}`);
    return null;
  }

  if (characteristic?.value) {
    const decodedData = decodeBleData(characteristic.value);

    if (decodedData) {
      console.log('🔵 Decoded BLE Data (Official Protocol):', decodedData);

      switch (decodedData.type) {
        case 'realTimeData':
          console.log('📊 Real-time Data:');
          console.log(`  Steps: ${decodedData.steps}`);
          console.log(`  Distance: ${decodedData.distance} KM`);
          console.log(`  Calories: ${decodedData.calories} KCAL`);
          console.log(`  Heart Rate: ${decodedData.heartRate} BPM`);
          console.log(`  Temperature: ${decodedData.temperature}°C`);
          console.log(`  Blood Oxygen: ${decodedData.bloodOxygen}%`);
          console.log(`  Active: ${decodedData.isActive}`);
          break;

        case 'healthMeasurement':
          console.log('🩺 Health Measurement:');
          console.log(`  Type: ${decodedData.measurementTypeName}`);
          console.log(`  Heart Rate: ${decodedData.heartRate} BPM`);
          console.log(`  Blood Oxygen: ${decodedData.bloodOxygen}%`);
          console.log(`  HRV: ${decodedData.hrv}`);
          console.log(`  Fatigue: ${decodedData.fatigueLevel}`);
          console.log(
            `  Blood Pressure: ${decodedData.highBloodPressure}/${decodedData.lowBloodPressure}`
          );
          break;

        case 'battery':
          console.log(
            `🔋 Battery: ${decodedData.batteryLevel}% (${decodedData.voltage}mV, ${decodedData.temperature}°C)`
          );
          break;

        case 'singleHeartRateData':
          console.log(`💓 Single Heart Rate Data: ${decodedData.totalReadings} readings`);
          console.log(`  Latest Heart Rate: ${decodedData.latestHeartRate} BPM`);
          if (decodedData.readings.length > 0) {
            const latest = decodedData.readings[decodedData.readings.length - 1];
            console.log(`  Latest Reading Time: ${latest.timestamp}`);
            console.log(`  Data ID: ${latest.dataId}`);
          }
          break;

        case 'deviceTime':
          console.log(
            `🕐 Device Time: ${decodedData.dateString} (Weekday: ${decodedData.weekday})`
          );
          break;

        case 'sportsMode':
          console.log(
            `🏃 Sports Mode: ${decodedData.modeName} - ${decodedData.success ? 'Success' : 'Failed'}`
          );
          break;

        case 'successResponse':
          console.log(`✅ Success: ${decodedData.commandName}`);
          break;

        case 'errorResponse':
          console.log(
            `❌ Error: ${decodedData.commandName} (Code: 0x${decodedData.errorCode.toString(16)})`
          );
          break;

        case 'unknownCommand':
          console.log(`❓ Unknown: ${decodedData.commandName}`);
          break;
      }
    }

    return decodedData;
  }

  return null;
}

// Command builders based on official protocol
export const BleCommands = {
  // Enable real-time step counting (triggers 0x86 responses)
  enableRealTimeStep: (enableStep: boolean = true, enableTemp: boolean = true): Uint8Array => {
    const data = new Uint8Array(16);
    data[0] = DeviceConst.CMD_REAL_TIME_STEP; // 0x09
    data[1] = enableStep ? 1 : 0;
    data[2] = enableTemp ? 1 : 0;

    // Calculate checksum
    let crc = 0;
    for (let i = 0; i < 15; i++) {
      crc += data[i];
    }
    data[15] = crc & 0xff;

    return data;
  },

  // Start health measurement
  startHealthMeasurement: (type: 1 | 2 | 3, duration: number = 60): Uint8Array => {
    const data = new Uint8Array(16);
    data[0] = DeviceConst.CMD_HEALTH_MEASUREMENT; // 0x28
    data[1] = type; // 1=HRV, 2=Heart Rate, 3=Blood Oxygen
    data[2] = 1; // Start measurement
    data[4] = duration & 0xff; // Duration low byte
    data[5] = (duration >> 8) & 0xff; // Duration high byte

    let crc = 0;
    for (let i = 0; i < 15; i++) {
      crc += data[i];
    }
    data[15] = crc & 0xff;

    return data;
  },

  // Get battery level
  getBattery: (): Uint8Array => {
    const data = new Uint8Array(16);
    data[0] = DeviceConst.CMD_GET_BATTERY; // 0x13
    data[1] = 0x98; // Perform power and temperature detection

    let crc = 0;
    for (let i = 0; i < 15; i++) {
      crc += data[i];
    }
    data[15] = crc & 0xff;

    return data;
  },
};

export type { BatteryData, BleDecodedData, HealthMeasurement, RealTimeData, SingleHeartRateData };
