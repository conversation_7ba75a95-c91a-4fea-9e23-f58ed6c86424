import { Stack } from 'expo-router';

export default function PatientsLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: 'transparent',
        },
        headerShadowVisible: false,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerTintColor: '#333',
        contentStyle: {
          backgroundColor: 'transparent',
        },
      }}
    >
      <Stack.Screen
        name="index"
        options={({ route }) => ({
          headerShown: true,
          title: (route.params as any)?.facilityName || 'Patients',
        })}
      />
      <Stack.Screen
        name="[patientId]"
        options={() => ({
          headerShown: false,
        })}
      />
      <Stack.Screen
        name="connect-device"
        options={{
          headerShown: true,
          title: 'Connect Device',
          presentation: 'modal',
        }}
      />
    </Stack>
  );
}
