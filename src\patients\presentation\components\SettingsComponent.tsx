import { COLORS } from '@/src/shared/business/constants/colors';
import { Modal } from '@/src/shared/presentation/components/Modal';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface Props {
  visible: boolean;
  onClose: () => void;
  maxValue: number;
  minValue: number;
  onMaxValueChange: (value: number) => void;
  onMinValueChange: (value: number) => void;
}

export const SettingsComponent = ({
  visible,
  onClose,
  maxValue,
  minValue,
  onMaxValueChange,
  onMinValueChange,
}: Props) => {
  return (
    <Modal isVisible={visible} onClose={onClose} width={250}>
      <View style={styles.modalContentHeader}>
        <View style={styles.modalContentHeaderTitle}>
          <Ionicons name="settings-outline" size={16} color={COLORS.TEXT_TITLE} />
          <Text style={styles.modalContentHeaderTitleText}>Chart Settings</Text>
        </View>
        <TouchableOpacity
          onPress={onClose}
          style={styles.modalContentHeaderCloseButton}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="close-outline" size={16} color={COLORS.WHITE} />
        </TouchableOpacity>
      </View>
      <View style={styles.modalContentBody}>
        <View style={styles.modalContentBodyItem}>
          <Text style={styles.modalContentBodyItemText}>Set Maximum</Text>
          <View style={styles.modalContentBodyItemValueContainer}>
            <TouchableOpacity
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={() => onMaxValueChange(maxValue - 1)}
            >
              <Ionicons name="chevron-back" size={16} color={COLORS.TEXT_MAIN} />
            </TouchableOpacity>
            <Text style={styles.modalContentBodyItemValue}>{maxValue}</Text>
            <TouchableOpacity
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={() => onMaxValueChange(maxValue + 1)}
            >
              <Ionicons name="chevron-forward" size={16} color={COLORS.TEXT_MAIN} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.modalContentBodyItem}>
          <Text style={styles.modalContentBodyItemText}>Set Minimum</Text>
          <View style={styles.modalContentBodyItemValueContainer}>
            <TouchableOpacity
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={() => onMinValueChange(minValue - 1)}
            >
              <Ionicons name="chevron-back" size={16} color={COLORS.TEXT_MAIN} />
            </TouchableOpacity>
            <Text style={styles.modalContentBodyItemValue}>{minValue}</Text>
            <TouchableOpacity
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={() => onMinValueChange(minValue + 1)}
            >
              <Ionicons name="chevron-forward" size={16} color={COLORS.TEXT_MAIN} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContentHeader: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalContentHeaderTitle: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 4,
  },
  modalContentHeaderTitleText: {
    fontSize: 12,
    fontWeight: '700',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_TITLE,
  },
  modalContentHeaderCloseButton: {
    backgroundColor: COLORS.ACCENT,
    padding: 3,
    borderRadius: 9999,
  },
  modalContentBody: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    gap: 16,
  },
  modalContentBodyItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    gap: 4,
  },
  modalContentBodyItemText: {
    fontSize: 10,
    fontWeight: '600',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_MAIN,
  },
  modalContentBodyItemValue: {
    fontSize: 12,
    fontWeight: '300',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_MAIN,
    minWidth: 30,
    textAlign: 'center',
  },
  modalContentBodyItemValueContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 4,
  },
});
