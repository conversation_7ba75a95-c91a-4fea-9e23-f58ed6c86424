import { Device } from 'react-native-ble-plx';
import { create } from 'zustand';

interface DeviceStore {
  activeDevice: Device | null;
  setActiveDevice: (device: Device | null) => void;
  resetActiveDevice: () => void;
}

export const useDeviceStore = create<DeviceStore>((set) => ({
  activeDevice: null,
  setActiveDevice: (device: Device | null) => set({ activeDevice: device }),
  resetActiveDevice: () => set({ activeDevice: null }),
}));
