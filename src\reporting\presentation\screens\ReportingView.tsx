import useJStyleRing from '@/src/shared/presentation/hooks/use-jstyle-ring';
import React from 'react';
import {
  <PERSON><PERSON>,
  FlatList,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { Device } from 'react-native-ble-plx';

const ReportingView = () => {
  // const {
  //   requestPermissions,
  //   connectToDevice,
  //   disconnectFromDevice,
  //   allDevices,
  //   connectedDevice,
  //   scanForPeripherals,
  //   stopScan,
  //   isConnecting,
  //   isScanning,
  //   // Heart rate testing - only keep notification method
  //   heartRateData,
  //   smartRingData,
  //   isRequestingData,
  //   getLatestHeartRateDataWithNotification,
  //   // Battery level functionality
  //   batteryLevel,
  //   isRequestingBattery,
  //   getBatteryLevel,
  //   // Oxygen level functionality
  //   oxygenLevel,
  //   isRequestingOxygen,
  //   getOxygenLevel,

  //   // Live measurement functionality
  //   liveMeasurementData,
  //   isLiveMeasuring,
  //   liveMeasurementType,
  //   startLiveHeartRateMeasurement,
  //   startLiveOxygenMeasurement,
  //   stopLiveMeasurement,
  //   liveMeasurementDuration,
  //   // Streaming data functionality
  //   startStreamingData,
  // } = useBLE();

  const {
    startRealTimeStepCounting,
    requestPermissions,
    connectedDevice,
    scanForRing,
    isScanning,
    isConnecting,
    stopScan,
    allDevices,
    isMeasuring,
    measurementType,
    healthData,
    connectToRing,
    disconnectFromRing,
    startHeartRateMeasurement,
    getBatteryLevel,
  } = useJStyleRing();

  const scanForDevices = async () => {
    try {
      const isPermissionsEnabled = await requestPermissions();
      if (isPermissionsEnabled) {
        scanForRing();
      } else {
        Alert.alert(
          'Permissions Required',
          'Bluetooth and Location permissions are required to scan for devices.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.log('Error requesting permissions:', error);
      Alert.alert('Error', 'Failed to request permissions');
    }
  };

  const handleConnectToDevice = async (device: Device) => {
    try {
      await connectToRing(device);
    } catch (error) {
      console.log('Connection failed:', error);
      Alert.alert('Connection Failed', 'Failed to connect to the device. Please try again.', [
        { text: 'OK' },
      ]);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnectFromRing();
    } catch (error) {
      console.log('Disconnection failed:', error);
      Alert.alert('Disconnection Failed', 'Failed to disconnect from the device.', [
        { text: 'OK' },
      ]);
    }
  };

  const getConnectionStatus = () => {
    if (isConnecting) return 'Connecting...';
    if (connectedDevice) return `Connected to ${connectedDevice.name || 'Unknown Device'}`;
    return 'Not Connected';
  };

  const renderDeviceItem = ({ item }: { item: Device }) => (
    <TouchableOpacity
      style={styles.deviceItem}
      onPress={() => handleConnectToDevice(item)}
      disabled={isConnecting}
    >
      <View style={styles.deviceInfo}>
        <Text style={styles.deviceName}>{item.name || item.localName || 'Unknown Device'}</Text>
        <Text style={styles.deviceId}>{item.id}</Text>
        {item.rssi && <Text style={styles.deviceRssi}>RSSI: {item.rssi} dBm</Text>}
      </View>
    </TouchableOpacity>
  );

  const handleStartHeartRate = async () => {
    if (!connectedDevice) {
      Alert.alert('Error', 'No device connected');
      return;
    }

    try {
      await startHeartRateMeasurement(30); // 30 seconds duration
      Alert.alert('Measurement Started', 'Heart rate measurement started for 30 seconds');
    } catch (error) {
      console.error('Heart rate measurement error:', error);
      Alert.alert('Error', 'Failed to start heart rate measurement');
    }
  };

  console.log('healthData', healthData);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        {/* Header Section */}
        <View style={styles.headerSection}>
          <Text style={styles.title}>J Style Smart Ring Testing</Text>
          <Text style={styles.connectionStatus}>{getConnectionStatus()}</Text>
          {connectedDevice && (
            <Text style={styles.deviceIdText}>Device ID: {connectedDevice.id}</Text>
          )}
        </View>

        {/* Scan Control */}
        <View style={styles.controlSection}>
          <TouchableOpacity
            style={[
              styles.scanButton,
              {
                backgroundColor: isScanning ? '#DC3545' : '#007BFF',
                opacity: isConnecting ? 0.6 : 1,
              },
            ]}
            onPress={isScanning ? stopScan : scanForDevices}
            disabled={isConnecting}
          >
            <Text style={styles.buttonText}>{isScanning ? 'Stop Scanning' : 'Start Scanning'}</Text>
          </TouchableOpacity>

          {connectedDevice && (
            <TouchableOpacity
              style={[styles.disconnectButton]}
              onPress={handleDisconnect}
              disabled={isConnecting}
            >
              <Text style={styles.buttonText}>Disconnect</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Device List */}
        <View style={styles.deviceSection}>
          <Text style={styles.sectionTitle}>Discovered Devices ({allDevices.length})</Text>

          {isScanning && (
            <View style={styles.scanningIndicator}>
              <Text style={styles.scanningText}>Scanning for devices...</Text>
            </View>
          )}

          <FlatList
            data={allDevices}
            keyExtractor={(item) => item.id}
            renderItem={renderDeviceItem}
            style={styles.deviceList}
            scrollEnabled={false}
            ListEmptyComponent={
              <View style={styles.emptyList}>
                <Text style={styles.emptyText}>
                  {isScanning
                    ? 'Searching for devices...'
                    : 'No devices found. Start scanning to discover devices.'}
                </Text>
              </View>
            }
          />
        </View>

        <View style={styles.heartRateSection}>
          <Text style={styles.sectionTitle}>Heart Rate Testing</Text>
          <TouchableOpacity
            style={[styles.heartRateButton, { backgroundColor: '#007BFF' }]}
            onPress={handleStartHeartRate}
            disabled={isConnecting}
          >
            <Text style={styles.buttonText}>Start Heart Rate Measurement</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.batterySection}>
          <Text style={styles.sectionTitle}>Battery Level</Text>
          <TouchableOpacity style={styles.batteryButton} onPress={getBatteryLevel}>
            <Text style={styles.buttonText}>Get Battery Level</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.batterySection}>
          <Text style={styles.sectionTitle}>Real-time Step Counting</Text>
          <TouchableOpacity style={styles.batteryButton} onPress={startRealTimeStepCounting}>
            <Text style={styles.buttonText}>Start Real-time Step Counting</Text>
          </TouchableOpacity>
        </View>

        {isMeasuring && (
          <View style={styles.heartRateSection}>
            <Text style={styles.sectionTitle}>Heart Rate Testing</Text>
          </View>
        )}

        {healthData && (
          <View style={styles.heartRateSection}>
            <Text style={styles.sectionTitle}>{healthData.heartRate}</Text>
          </View>
        )}

        {measurementType && (
          <View style={styles.heartRateSection}>
            <Text style={styles.sectionTitle}>{measurementType}</Text>
          </View>
        )}

        {/* Real-time Streaming Data Section - Only show when connected */}
        {/* {connectedDevice && (
          <View style={styles.streamingDataSection}>
            <Text style={styles.sectionTitle}>Real-time Streaming Data</Text>
            <Text style={styles.streamingDataSubtitle}>Live data from the smart ring device</Text>

            <View style={styles.streamingDataControls}>
              <TouchableOpacity
                style={[styles.streamingDataButton, { backgroundColor: '#007BFF' }]}
                onPress={() => startStreamingData(connectedDevice)}
              >
                <Text style={styles.buttonText}>Start Data Streaming</Text>
              </TouchableOpacity>
            </View>

            {liveMeasurementDuration && (
              <View style={styles.streamingDataCard}>
                <Text style={styles.streamingDataTitle}>
                  Latest {liveMeasurementDuration.type} Data
                </Text>

                {liveMeasurementDuration.type === 'realTimeData' && (
                  <View style={styles.realTimeDataGrid}>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Steps</Text>
                      <Text style={styles.dataValue}>{liveMeasurementDuration.steps || 0}</Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Distance</Text>
                      <Text style={styles.dataValue}>
                        {(liveMeasurementDuration.distance || 0).toFixed(2)} km
                      </Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Calories</Text>
                      <Text style={styles.dataValue}>
                        {(liveMeasurementDuration.calories || 0).toFixed(0)} kcal
                      </Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Temperature</Text>
                      <Text style={styles.dataValue}>
                        {(liveMeasurementDuration.temperature || 0).toFixed(1)}°C
                      </Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Heart Rate</Text>
                      <Text style={styles.dataValue}>
                        {liveMeasurementDuration.heartRate || 0} BPM
                      </Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Blood Oxygen</Text>
                      <Text style={styles.dataValue}>
                        {liveMeasurementDuration.bloodOxygen || 0}%
                      </Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Status</Text>
                      <Text style={styles.dataValue}>
                        {liveMeasurementDuration.isActive ? '🟢 Active' : '🔴 Inactive'}
                      </Text>
                    </View>
                  </View>
                )}

                {liveMeasurementDuration.type === 'healthMeasurement' && (
                  <View style={styles.healthMeasurementGrid}>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Measurement Type</Text>
                      <Text style={styles.dataValue}>
                        {liveMeasurementDuration.measurementTypeName || 'Unknown'}
                      </Text>
                    </View>
                    {liveMeasurementDuration.heartRate > 0 && (
                      <View style={styles.dataItem}>
                        <Text style={styles.dataLabel}>Heart Rate</Text>
                        <Text style={styles.dataValue}>
                          {liveMeasurementDuration.heartRate} BPM
                        </Text>
                      </View>
                    )}
                    {liveMeasurementDuration.bloodOxygen > 0 && (
                      <View style={styles.dataItem}>
                        <Text style={styles.dataLabel}>Blood Oxygen</Text>
                        <Text style={styles.dataValue}>{liveMeasurementDuration.bloodOxygen}%</Text>
                      </View>
                    )}
                    {liveMeasurementDuration.hrv > 0 && (
                      <View style={styles.dataItem}>
                        <Text style={styles.dataLabel}>HRV</Text>
                        <Text style={styles.dataValue}>{liveMeasurementDuration.hrv} ms</Text>
                      </View>
                    )}
                    {liveMeasurementDuration.fatigueLevel > 0 && (
                      <View style={styles.dataItem}>
                        <Text style={styles.dataLabel}>Fatigue Level</Text>
                        <Text style={styles.dataValue}>{liveMeasurementDuration.fatigueLevel}</Text>
                      </View>
                    )}
                    {(liveMeasurementDuration.highBloodPressure > 0 ||
                      liveMeasurementDuration.lowBloodPressure > 0) && (
                      <View style={styles.dataItem}>
                        <Text style={styles.dataLabel}>Blood Pressure</Text>
                        <Text style={styles.dataValue}>
                          {liveMeasurementDuration.highBloodPressure}/
                          {liveMeasurementDuration.lowBloodPressure} mmHg
                        </Text>
                      </View>
                    )}
                  </View>
                )}

                {liveMeasurementDuration.type === 'battery' && (
                  <View style={styles.batteryDataGrid}>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Battery Level</Text>
                      <Text style={styles.dataValue}>{liveMeasurementDuration.batteryLevel}%</Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Voltage</Text>
                      <Text style={styles.dataValue}>{liveMeasurementDuration.voltage} mV</Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Temperature</Text>
                      <Text style={styles.dataValue}>{liveMeasurementDuration.temperature}°C</Text>
                    </View>
                  </View>
                )}

                {liveMeasurementDuration.type === 'deviceTime' && (
                  <View style={styles.deviceTimeGrid}>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Device Time</Text>
                      <Text style={styles.dataValue}>{liveMeasurementDuration.dateString}</Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Weekday</Text>
                      <Text style={styles.dataValue}>{liveMeasurementDuration.weekday}</Text>
                    </View>
                  </View>
                )}

                {liveMeasurementDuration.type === 'sportsMode' && (
                  <View style={styles.sportsModeGrid}>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Sports Mode</Text>
                      <Text style={styles.dataValue}>{liveMeasurementDuration.modeName}</Text>
                    </View>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Status</Text>
                      <Text style={styles.dataValue}>
                        {liveMeasurementDuration.success ? '✅ Success' : '❌ Failed'}
                      </Text>
                    </View>
                  </View>
                )}

                {(liveMeasurementDuration.type === 'successResponse' ||
                  liveMeasurementDuration.type === 'errorResponse' ||
                  liveMeasurementDuration.type === 'unknownCommand') && (
                  <View style={styles.commandResponseGrid}>
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Command</Text>
                      <Text style={styles.dataValue}>{liveMeasurementDuration.commandName}</Text>
                    </View>
                    {liveMeasurementDuration.type === 'errorResponse' && (
                      <View style={styles.dataItem}>
                        <Text style={styles.dataLabel}>Error Code</Text>
                        <Text style={styles.dataValue}>
                          0x{liveMeasurementDuration.errorCode?.toString(16)}
                        </Text>
                      </View>
                    )}
                    <View style={styles.dataItem}>
                      <Text style={styles.dataLabel}>Status</Text>
                      <Text style={styles.dataValue}>
                        {liveMeasurementDuration.type === 'successResponse'
                          ? '✅ Success'
                          : liveMeasurementDuration.type === 'errorResponse'
                            ? '❌ Error'
                            : '❓ Unknown'}
                      </Text>
                    </View>
                  </View>
                )}

                <View style={styles.rawDataSection}>
                  <Text style={styles.rawDataTitle}>Raw Data</Text>
                  <Text style={styles.rawDataText}>
                    Command: 0x
                    {liveMeasurementDuration.command?.toString(16).padStart(2, '0') || 'N/A'}
                  </Text>
                  <Text style={styles.rawDataText}>
                    Hex: {liveMeasurementDuration.hex?.substring(0, 32) || 'N/A'}...
                  </Text>
                  <Text style={styles.rawDataText}>
                    Timestamp: {new Date().toLocaleTimeString()}
                  </Text>
                </View>
              </View>
            )}

            {!liveMeasurementDuration && (
              <View style={styles.noStreamingDataCard}>
                <Text style={styles.noStreamingDataText}>
                  No streaming data yet. Press "Start Data Streaming" to begin receiving live data
                  from the device.
                </Text>
              </View>
            )}
          </View>
        )} */}

        {/* Heart Rate Testing Section - Only show when connected */}
        {connectedDevice && (
          <View style={styles.heartRateSection}>
            <Text style={styles.sectionTitle}>Heart Rate Testing</Text>

            {/* Heart Rate Controls */}
            {/* <View style={styles.heartRateControls}>
              <TouchableOpacity
                style={[styles.heartRateButton, { backgroundColor: '#007BFF' }]}
                onPress={getLatestHeartRateDataWithNotification}
                disabled={isRequestingData}
              >
                <Text style={styles.buttonText}>
                  {isRequestingData ? 'Reading...' : 'Single (Notify)'}
                </Text>
              </TouchableOpacity>
            </View> */}

            {/* {smartRingData && (
              <View style={styles.heartRateDataCard}>
                <Text style={styles.heartRateDataTitle}>
                  {smartRingData.commandType === 0x55 ? 'Single Reading' : 'Continuous Data'}
                </Text>

                {smartRingData.commandType === 0x55 ? (
                  <View style={styles.heartRateDisplay}>
                    <Text style={styles.heartRateValue}>{smartRingData.heartRate}</Text>
                    <Text style={styles.heartRateUnit}>BPM</Text>
                  </View>
                ) : (
                  <View style={styles.heartRateDisplay}>
                    <Text style={styles.heartRateValue}>
                      {smartRingData.readings.length > 0
                        ? smartRingData.readings[smartRingData.readings.length - 1].heartRate
                        : '--'}
                    </Text>
                    <Text style={styles.heartRateUnit}>BPM</Text>
                    <Text style={styles.readingsCount}>
                      {smartRingData.readings.length} readings
                    </Text>
                  </View>
                )}

                <View style={styles.heartRateDetails}>
                  <Text style={styles.detailText}>Data ID: {smartRingData.dataId}</Text>
                  <Text style={styles.detailText}>
                    Time: {smartRingData.timestamp.toLocaleTimeString()}
                  </Text>
                  <Text style={styles.detailText}>
                    Command: 0x{smartRingData.commandType.toString(16).toUpperCase()}
                  </Text>
                </View>
              </View>
            )}

            {heartRateData && !smartRingData && (
              <View style={styles.heartRateDataCard}>
                <Text style={styles.heartRateDataTitle}>Legacy Heart Rate</Text>
                <View style={styles.heartRateDisplay}>
                  <Text style={styles.heartRateValue}>{heartRateData.heartRate}</Text>
                  <Text style={styles.heartRateUnit}>BPM</Text>
                </View>
                <View style={styles.heartRateDetails}>
                  <Text style={styles.detailText}>
                    Contact: {heartRateData.sensorContact ? 'Good' : 'Poor'}
                  </Text>
                  <Text style={styles.detailText}>
                    Time: {heartRateData.timestamp.toLocaleTimeString()}
                  </Text>
                </View>
              </View>
            )}

            {!smartRingData && !heartRateData && (
              <View style={styles.noDataCard}>
                <Text style={styles.noDataText}>
                  No heart rate data yet. Press a button above to start reading.
                </Text>
              </View>
            )}
          </View>
        )} */}

            {/* Battery Level Section - Only show when connected */}
            {/* {connectedDevice && (
          <View style={styles.batterySection}>
            <Text style={styles.sectionTitle}>Device Battery</Text>

            <View style={styles.batteryControls}>
              <TouchableOpacity
                style={[styles.batteryButton, { backgroundColor: '#28A745' }]}
                onPress={getBatteryLevel}
                disabled={isRequestingBattery}
              >
                <Text style={styles.buttonText}>
                  {isRequestingBattery ? 'Reading...' : 'Get Battery Level'}
                </Text>
              </TouchableOpacity>
            </View>

            {batteryLevel !== null && (
              <View style={styles.batteryDataCard}>
                <Text style={styles.batteryDataTitle}>Current Battery Level</Text>
                <View style={styles.batteryDisplay}>
                  <Text style={styles.batteryValue}>{batteryLevel}</Text>
                  <Text style={styles.batteryUnit}>%</Text>
                </View>
                <View style={styles.batteryDetails}>
                  <Text style={styles.detailText}>
                    Status: {batteryLevel > 20 ? 'Good' : batteryLevel > 10 ? 'Low' : 'Critical'}
                  </Text>
                  <Text style={styles.detailText}>
                    Last Updated: {new Date().toLocaleTimeString()}
                  </Text>
                </View>
              </View>
            )}


            {batteryLevel === null && (
              <View style={styles.noBatteryDataCard}>
                <Text style={styles.noBatteryDataText}>
                  No battery data yet. Press the button above to check battery level.
                </Text>
              </View>
            )}
          </View>
        )} */}

            {/* Oxygen Level Section - Only show when connected */}
            {/* {connectedDevice && (
          <View style={styles.oxygenSection}>
            <Text style={styles.sectionTitle}>Blood Oxygen (SpO2)</Text>

            <View style={styles.oxygenControls}>
              <TouchableOpacity
                style={[styles.oxygenButton, { backgroundColor: '#FF6B35' }]}
                onPress={getOxygenLevel}
                disabled={isRequestingOxygen}
              >
                <Text style={styles.buttonText}>
                  {isRequestingOxygen ? 'Reading...' : 'Get Oxygen Level'}
                </Text>
              </TouchableOpacity>
            </View>

            {oxygenLevel !== null && (
              <View style={styles.oxygenDataCard}>
                <Text style={styles.oxygenDataTitle}>Current Blood Oxygen Level</Text>
                <View style={styles.oxygenDisplay}>
                  <Text style={styles.oxygenValue}>{oxygenLevel}</Text>
                  <Text style={styles.oxygenUnit}>%</Text>
                </View>
                <View style={styles.oxygenDetails}>
                  <Text style={styles.detailText}>
                    Status: {oxygenLevel >= 95 ? 'Normal' : oxygenLevel >= 90 ? 'Low' : 'Critical'}
                  </Text>
                  <Text style={styles.detailText}>
                    Last Updated: {new Date().toLocaleTimeString()}
                  </Text>
                </View>
              </View>
            )}

            {oxygenLevel === null && (
              <View style={styles.noOxygenDataCard}>
                <Text style={styles.noOxygenDataText}>
                  No oxygen data yet. Press the button above to check blood oxygen level.
                </Text>
              </View>
            )}
          </View>
        )} */}

            {/* Live Measurements Section - Only show when connected */}
            {/* {connectedDevice && (
          <View style={styles.liveMeasurementSection}>
            <Text style={styles.sectionTitle}>Live Measurements</Text>
            <Text style={styles.liveMeasurementSubtitle}>
              Trigger real-time measurements (vs reading stored data)
            </Text> */}

            {/* Live Measurement Controls */}
            {/* <View style={styles.liveMeasurementControls}>
              <TouchableOpacity
                style={[
                  styles.liveMeasurementButton,
                  {
                    backgroundColor:
                      isLiveMeasuring && liveMeasurementType === 2 ? '#DC3545' : '#007BFF',
                  },
                ]}
                onPress={
                  isLiveMeasuring && liveMeasurementType === 2
                    ? stopLiveMeasurement
                    : startLiveHeartRateMeasurement
                }
                disabled={isLiveMeasuring && liveMeasurementType !== 2}
              >
                {liveMeasurementDuration && (
                  <Text style={styles.buttonText}>{JSON.stringify(liveMeasurementDuration)}</Text>
                )}
                <Text style={styles.buttonText}>
                  {isLiveMeasuring && liveMeasurementType === 2
                    ? 'Stop Heart Rate'
                    : 'Live Heart Rate (30s)'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.liveMeasurementButton,
                  {
                    backgroundColor:
                      isLiveMeasuring && liveMeasurementType === 3 ? '#DC3545' : '#FF6B35',
                  },
                ]}
                onPress={
                  isLiveMeasuring && liveMeasurementType === 3
                    ? stopLiveMeasurement
                    : () => startLiveOxygenMeasurement(30)
                }
                disabled={isLiveMeasuring && liveMeasurementType !== 3}
              >
                <Text style={styles.buttonText}>
                  {isLiveMeasuring && liveMeasurementType === 3
                    ? 'Stop Oxygen'
                    : 'Live Oxygen (30s)'}
                </Text>
              </TouchableOpacity>
            </View> */}

            {/* Live Measurement Status */}
            {/* {isLiveMeasuring && (
              <View style={styles.liveMeasurementStatus}>
                <Text style={styles.liveMeasurementStatusText}>
                  🔴 Live {liveMeasurementType === 2 ? 'Heart Rate' : 'Oxygen'} measurement in
                  progress...
                </Text>
              </View>
            )} */}

            {/* Live Measurement Data Display */}
            {/* {liveMeasurementData && (
              <View style={styles.liveMeasurementDataCard}>
                <Text style={styles.liveMeasurementDataTitle}>
                  Live {liveMeasurementData.measurementType === 2 ? 'Heart Rate' : 'Oxygen'}{' '}
                  Measurement
                </Text>

                {/* Show measurement value if available */}
            {/* {liveMeasurementData.data?.value !== null &&
                  liveMeasurementData.data?.value !== undefined && (
                    <View style={styles.liveMeasurementValueDisplay}>
                      <Text style={styles.liveMeasurementBigValue}>
                        {liveMeasurementData.data.value}
                      </Text>
                      <Text style={styles.liveMeasurementBigUnit}>
                        {liveMeasurementData.measurementType === 2 ? 'BPM' : '%'}
                      </Text>
                    </View>
                  )} */}

            {/* <View style={styles.liveMeasurementDisplay}>
                  <Text style={styles.liveMeasurementValue}>
                    Type: {liveMeasurementData.measurementType === 2 ? 'Heart Rate' : 'Oxygen'}
                  </Text>
                  <Text style={styles.liveMeasurementUnit}>
                    Status: {liveMeasurementData.isComplete ? 'Complete' : 'In Progress'}
                  </Text>
                </View> */}

            {/* Official SDK Format Information */}
            {/* {liveMeasurementData.data?.officialSDKFormat && (
                  <View style={styles.liveMeasurementOfficialFormat}>
                    <Text style={styles.officialFormatTitle}>Official SDK Pattern:</Text>
                    <Text style={styles.detailText}>
                      DataType: {liveMeasurementData.data.officialSDKFormat.DataType}
                    </Text>
                    <Text style={styles.detailText}>
                      End: {liveMeasurementData.data.officialSDKFormat.End ? 'true' : 'false'}
                    </Text>
                    {liveMeasurementData.data.dataMap &&
                      Object.keys(liveMeasurementData.data.dataMap).length > 0 && (
                        <Text style={styles.detailText}>
                          Data:{' '}
                          {Object.entries(liveMeasurementData.data.dataMap)
                            .map(([key, value]) => `${key}=${value}`)
                            .join(', ')}
                        </Text>
                      )}
                  </View>
                )} */}

            {/* {liveMeasurementData.data && (
                  <View style={styles.liveMeasurementDebug}>
                    <Text style={styles.detailText}>
                      Measurement Value:{' '}
                      {liveMeasurementData.data.value !== null &&
                      liveMeasurementData.data.value !== undefined
                        ? liveMeasurementData.data.value
                        : 'N/A'}
                    </Text>
                    <Text style={styles.detailText}>
                      Status Byte: 0x
                      {liveMeasurementData.data.status?.toString(16).padStart(2, '0') || 'N/A'}
                    </Text>
                    {liveMeasurementData.data.callbackType && (
                      <Text style={styles.detailText}>
                        Callback: {liveMeasurementData.data.callbackType}
                      </Text>
                    )}
                    {liveMeasurementData.data.source && (
                      <Text style={styles.detailText}>
                        Source: {liveMeasurementData.data.source}
                      </Text>
                    )}
                    <Text style={styles.detailText}>
                      Raw Data:{' '}
                      {liveMeasurementData.rawData
                        ?.slice(0, 8)
                        .map((b: number) => '0x' + b.toString(16).padStart(2, '0'))
                        .join(' ') || 'N/A'}
                    </Text>
                  </View>
                )}

                <View style={styles.liveMeasurementDetails}>
                  <Text style={styles.detailText}>
                    Command: 0x{liveMeasurementData.commandType.toString(16).toUpperCase()}
                  </Text>
                  <Text style={styles.detailText}>
                    Time: {liveMeasurementData.timestamp.toLocaleTimeString()}
                  </Text>
                </View>
              </View>
            )}  */}

            {/* No Live Data State */}
            {/* {!liveMeasurementData && !isLiveMeasuring && (
              <View style={styles.noLiveMeasurementDataCard}>
                <Text style={styles.noLiveMeasurementDataText}>
                  No live measurements yet. Press a button above to start a real-time measurement.
                </Text>
              </View>
            )} */}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flexGrow: 1, // Allow content to grow and take available space
    padding: 16,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  connectionStatus: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
    marginBottom: 8,
  },
  deviceIdText: {
    fontSize: 12,
    color: '#999',
    fontFamily: 'monospace',
  },
  controlSection: {
    marginBottom: 24,
  },
  scanButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  disconnectButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: '#DC3545',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  deviceSection: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  scanningIndicator: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 12,
  },
  scanningText: {
    fontSize: 14,
    color: '#007BFF',
    fontStyle: 'italic',
  },
  deviceList: {
    flex: 1,
  },
  deviceItem: {
    backgroundColor: '#ffffff',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  deviceId: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  deviceRssi: {
    fontSize: 12,
    color: '#999',
  },
  emptyList: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  heartRateSection: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  heartRateControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  heartRateButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    minWidth: 200,
  },
  heartRateDataCard: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  heartRateDataTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  heartRateDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  heartRateValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007BFF',
  },
  heartRateUnit: {
    fontSize: 16,
    color: '#666',
  },
  readingsCount: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  heartRateDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  detailText: {
    fontSize: 12,
    color: '#666',
  },
  noDataCard: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noDataText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  batterySection: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  batteryControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  batteryButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    minWidth: 200,
    backgroundColor: '#007BFF',
  },
  batteryDataCard: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  batteryDataTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  batteryDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  batteryValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#28A745',
  },
  batteryUnit: {
    fontSize: 16,
    color: '#666',
  },
  batteryDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  noBatteryDataCard: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noBatteryDataText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  oxygenSection: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  oxygenControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  oxygenButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    minWidth: 200,
  },
  oxygenDataCard: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  oxygenDataTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  oxygenDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  oxygenValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  oxygenUnit: {
    fontSize: 16,
    color: '#666',
  },
  oxygenDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  noOxygenDataCard: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noOxygenDataText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  liveMeasurementSection: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  liveMeasurementSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  liveMeasurementControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  liveMeasurementButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    minWidth: 150,
  },
  liveMeasurementStatus: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 16,
  },
  liveMeasurementStatusText: {
    fontSize: 16,
    color: '#DC3545',
    fontWeight: 'bold',
  },
  liveMeasurementDataCard: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  liveMeasurementDataTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  liveMeasurementDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  liveMeasurementValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007BFF',
  },
  liveMeasurementUnit: {
    fontSize: 14,
    color: '#666',
  },
  liveMeasurementDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  noLiveMeasurementDataCard: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noLiveMeasurementDataText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  liveMeasurementValueDisplay: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'baseline',
    marginVertical: 12,
    paddingVertical: 8,
    backgroundColor: '#e8f5e8',
    borderRadius: 8,
  },
  liveMeasurementBigValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007BFF',
    marginRight: 8,
  },
  liveMeasurementBigUnit: {
    fontSize: 18,
    color: '#666',
    fontWeight: '500',
  },
  liveMeasurementDebug: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderRadius: 4,
    marginVertical: 4,
  },
  liveMeasurementOfficialFormat: {
    backgroundColor: '#e8f5e8',
    padding: 8,
    borderRadius: 4,
    marginVertical: 4,
    borderLeftWidth: 3,
    borderLeftColor: '#28A745',
  },
  officialFormatTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#28A745',
    marginBottom: 4,
  },
  // Streaming Data Styles
  streamingDataSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  streamingDataSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  streamingDataControls: {
    marginBottom: 16,
  },
  streamingDataButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 6,
    alignItems: 'center',
  },
  streamingDataCard: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  streamingDataTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  realTimeDataGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  healthMeasurementGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  batteryDataGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  deviceTimeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  sportsModeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  commandResponseGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  dataItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  dataLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  dataValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  rawDataSection: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 6,
    marginTop: 12,
  },
  rawDataTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  rawDataText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  noStreamingDataCard: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noStreamingDataText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default ReportingView;
