import { COLORS } from '@/src/shared/business/constants/colors';
import DataTable, { DataTableColumn } from '@/src/shared/presentation/components/DataTable';
import { Modal } from '@/src/shared/presentation/components/Modal';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface Props {
  isVisible: boolean;
  onClose: () => void;
  title: string;
}

export const ReportingComponent = ({ isVisible, onClose, title }: Props) => {
  interface ReportRecord {
    id: number;
    device: string;
    date: string;
    metric: number;
    status: 'normal' | 'low' | 'critical';
  }

  const getStatusChip = (status: string) => {
    const statusColors = {
      normal: {
        backgroundColor: COLORS.GREEN,
        textColor: '#FFFFFF',
      },
      low: {
        backgroundColor: COLORS.YELLOW,
        textColor: '#000000',
      },
      critical: {
        backgroundColor: COLORS.RED,
        textColor: '#FFFFFF',
      },
    };

    const colorConfig = statusColors[status as keyof typeof statusColors] || {
      backgroundColor: COLORS.BACKGROUND_GRAY,
      textColor: '#FFFFFF',
    };

    return (
      <View style={[styles.statusChip, { backgroundColor: colorConfig.backgroundColor }]}>
        <Text style={[styles.statusText, { color: colorConfig.textColor }]}>{status}</Text>
      </View>
    );
  };

  const columns: DataTableColumn<ReportRecord>[] = [
    {
      header: 'Device',
      key: 'device',
      width: 25,
    },
    {
      header: 'Date',
      key: 'date',
      width: 30,
    },
    {
      header: 'Metric',
      key: 'metric',
      width: 25,
    },
    {
      header: 'Status',
      key: 'status',
      width: 20,
      render: (item, value) => getStatusChip(value),
    },
  ];

  const data: ReportRecord[] = [
    {
      id: 1,
      device: 'ring',
      date: '10/10/2025',
      metric: 100,
      status: 'normal',
    },
    {
      id: 2,
      device: 'ring',
      date: '10/11/2025',
      metric: 95,
      status: 'normal',
    },
    {
      id: 3,
      device: 'ring',
      date: '10/12/2025',
      metric: 110,
      status: 'low',
    },
    {
      id: 4,
      device: 'ring',
      date: '10/13/2025',
      metric: 88,
      status: 'normal',
    },
    {
      id: 5,
      device: 'ring',
      date: '10/14/2025',
      metric: 120,
      status: 'critical',
    },
  ];

  const handleClose = () => {
    onClose();
  };
  return (
    <Modal isVisible={isVisible} onClose={onClose} width={400}>
      <View style={styles.headerContainer}>
        <View style={styles.titleContainer}>
          <Ionicons name="document-text-outline" size={16} color={COLORS.TEXT_TITLE} />
          <Text style={styles.title}>{title}</Text>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.downloadButton}>
            <Ionicons name="download-outline" size={12} color={COLORS.WHITE} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Ionicons name="close-outline" size={12} color={COLORS.WHITE} />
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.container}>
        <DataTable data={data} columns={columns} keyExtractor={(item) => item.id.toString()} />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    minHeight: 250,
  },
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  titleContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_TITLE,
  },
  buttonContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  downloadButton: {
    padding: 8,
    borderRadius: 999,
    backgroundColor: COLORS.ACCENT,
  },
  closeButton: {
    padding: 8,
    borderRadius: 999,
    backgroundColor: COLORS.ACCENT,
  },

  statusChip: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    minWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusText: {
    fontSize: 8,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
});
