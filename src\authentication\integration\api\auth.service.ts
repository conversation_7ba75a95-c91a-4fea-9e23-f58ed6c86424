import { CORE_DEPENDENCY_INJECTION_TYPES } from '@/src/core/dependency-injection/types';
import { AxiosService } from '@/src/core/services/axios.service';
import { inject, injectable } from 'inversify';
import { IAuthService } from '../../business/api/auth.service';

@injectable()
export class AuthService implements IAuthService {
  constructor(
    @inject(CORE_DEPENDENCY_INJECTION_TYPES.AxiosService)
    private readonly axiosService: AxiosService
  ) {}
  async checkToken(token: string): Promise<{ data: { isTokenValid: boolean } }> {
    try {
      const response = await this.axiosService.axios.get('/technician_profile', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return { data: { isTokenValid: Boolean(response.data.first_name) } };
    } catch (error) {
      console.log(error);
      return { data: { isTokenValid: false } };
    }
  }

  async login(email: string, password: string): Promise<{ data: { token: string } }> {
    try {
      return await this.axiosService.axios.post('/technicians/login', { email, password });
    } catch (error) {
      console.log(error);
      return Promise.resolve({ data: { token: '' } });
    }
  }

  async forgotPassword(email: string): Promise<{ data: any }> {
    try {
      return await this.axiosService.axios.post('/forgot-password', { email });
    } catch (error) {
      // console.log(error);
      return Promise.resolve({ data: {} });
    }
  }
}
