import React from 'react';
import { StyleSheet, Text, View, useWindowDimensions } from 'react-native';
import { LineChart } from 'react-native-gifted-charts';
import { Card } from './Card';

interface PatientsComplianceProps {
  totalPatients: number;
  totalReadings: number;
  changePercentage: string;
  chartData?: {
    patients: number[];
    readings: number[];
    months: string[];
  };
}

export const PatientsCompliance: React.FC<PatientsComplianceProps> = ({
  totalPatients,
  totalReadings,
  changePercentage,
  chartData,
}) => {
  const defaultChartData = {
    patients: [100, 250, 200, 450, 700, 200, 550, 200, 600, 580],
    readings: [350, 500, 450, 750, 850, 750, 800, 750, 770, 580],
    months: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'],
  };

  const data = chartData || defaultChartData;

  // Calculate responsive chart width
  const { width: screenWidth } = useWindowDimensions();
  const chartWidth = screenWidth;

  // Prepare data for the chart library
  const patientsData = data.patients.map((value, index) => ({
    value,
    label: data.months[index] || '',
    labelTextStyle: { color: '#8B939A', fontSize: 12 },
  }));

  const readingsData = data.readings.map((value) => ({
    value,
  }));

  return (
    <Card title="Patients and Compliance">
      <View style={styles.container}>
        {/* Metrics Section */}
        <View style={styles.metricsContainer}>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>{totalPatients.toLocaleString()}</Text>
            <Text style={styles.metricLabel}>Total Patients</Text>
          </View>
          <View style={styles.metricItem}>
            <Text style={[styles.metricValue, styles.readingsValue]}>
              {totalReadings.toLocaleString()}
            </Text>
            <Text style={styles.metricLabel}>Readings</Text>
          </View>
          <View style={styles.changeContainer}>
            <Text style={styles.changeText}>{changePercentage}</Text>
          </View>
        </View>

        {/* Chart Container */}
        <View style={styles.chartContainer}>
          <LineChart
            data={patientsData}
            data2={readingsData}
            height={180}
            color1="#4F80FF"
            color2="#FFB800"
            thickness1={3}
            thickness2={3}
            curved={true}
            areaChart={true}
            areaChart2={true}
            startFillColor1="#4F80FF"
            startFillColor2="#FFB800"
            endFillColor1="rgba(79, 128, 255, 0.1)"
            endFillColor2="rgba(255, 184, 0, 0.1)"
            startOpacity={0.4}
            endOpacity={0.1}
            spacing={Math.max(20, (chartWidth - 60) / data.patients.length)}
            initialSpacing={10}
            endSpacing={10}
            noOfSections={4}
            maxValue={800}
            yAxisColor="#E5E7EB"
            xAxisColor="#E5E7EB"
            yAxisThickness={1}
            xAxisThickness={1}
            rulesType="solid"
            rulesColor="#F3F4F6"
            yAxisTextStyle={{ color: '#8B939A', fontSize: 12 }}
            xAxisLabelTextStyle={{ color: '#8B939A', fontSize: 12 }}
            hideDataPoints1={false}
            hideDataPoints2={false}
            dataPointsColor1="#4F80FF"
            dataPointsColor2="#FFB800"
            dataPointsRadius={4}
            focusEnabled={true}
            showStripOnFocus={true}
            stripColor="#E5E7EB"
            stripHeight={180}
            stripOpacity={0.2}
            stripWidth={1}
            showTextOnFocus={true}
            focusedDataPointColor="#2563EB"
            focusedDataPointRadius={6}
            textFontSize={12}
            textColor1="#4F80FF"
            textColor2="#FFB800"
            animateOnDataChange={true}
            animationDuration={800}
          />
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 20,
  },
  metricsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingBottom: 20,
  },
  metricItem: {
    flex: 1,
  },
  metricValue: {
    fontSize: 32,
    fontWeight: '700',
    color: '#4F80FF',
    fontFamily: 'DMSans',
    marginBottom: 4,
  },
  readingsValue: {
    color: '#FFB800',
  },
  metricLabel: {
    fontSize: 14,
    color: '#8B939A',
    fontFamily: 'DMSans',
  },
  changeContainer: {
    alignItems: 'flex-end',
  },
  changeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#10B981',
    fontFamily: 'DMSans',
  },
  chartContainer: {
    flex: 1,
    paddingVertical: 10,
    overflow: 'hidden',
  },
});
