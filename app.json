{"expo": {"name": "RPM-App", "slug": "RPM-App", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "rpmapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "icon": {"light": "./assets/images/logo.png", "dark": "./assets/images/logo.png", "tinted": "./assets/images/logo.png"}, "infoPlist": {"NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true, "NSExceptionDomains": {"staging-api.moxie-link.com": {"NSExceptionAllowsInsecureHTTPLoads": false, "NSExceptionMinimumTLSVersion": "TLSv1.0", "NSExceptionRequiresForwardSecrecy": false}}}, "NSBluetoothAlwaysUsageDescription": "This app uses Bluetooth to connect to and communicate with external devices.", "NSBluetoothPeripheralUsageDescription": "This app uses Bluetooth to connect to and communicate with external devices.", "NSLocationWhenInUseUsageDescription": "This app uses location services to discover nearby Bluetooth devices."}, "bundleIdentifier": "com.moxielink.rpm"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_ADVERTISE", "android.permission.BLUETOOTH_CONNECT", "android.permission.BLUETOOTH_SCAN", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"], "edgeToEdgeEnabled": true, "package": "com.moxielink.rpm", "usesCleartextTraffic": true, "networkSecurityConfig": "./android/app/src/main/res/xml/network_security_config.xml"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logo.png"}, "plugins": ["expo-router", ["expo-font", {"fonts": ["node_modules/@expo-google-fonts/inter/900Black/Inter_900Black.ttf", "node_modules/@expo-google-fonts/inter/400Regular/Inter_400Regular.ttf", "node_modules/@expo-google-fonts/inter/500Medium/Inter_500Medium.ttf", "node_modules/@expo-google-fonts/inter/600SemiBold/Inter_600SemiBold.ttf", "node_modules/@expo-google-fonts/poppins/500Medium/Poppins_500Medium.ttf", "node_modules/@expo-google-fonts/poppins/600SemiBold/Poppins_600SemiBold.ttf", "node_modules/@expo-google-fonts/poppins/700Bold/Poppins_700Bold.ttf", "node_modules/@expo-google-fonts/poppins/800ExtraBold/Poppins_800ExtraBold.ttf", "node_modules/@expo-google-fonts/poppins/900Black/Poppins_900Black.ttf", "node_modules/@expo-google-fonts/dm-sans/400Regular/DMSans_400Regular.ttf", "node_modules/@expo-google-fonts/dm-sans/500Medium/DMSans_500Medium.ttf", "node_modules/@expo-google-fonts/dm-sans/700Bold/DMSans_700Bold.ttf"]}], ["expo-splash-screen", {"image": "./assets/images/logo.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["react-native-ble-plx", {"isBackgroundEnabled": true, "modes": ["peripheral", "central"], "bluetoothAlwaysPermission": "Allow $(PRODUCT_NAME) to connect to bluetooth devices"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "6bb85f92-4c5f-42da-b892-2ca3c0e76a62"}}, "owner": "luarte<PERSON>", "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/6bb85f92-4c5f-42da-b892-2ca3c0e76a62"}}}