import { FlashList, ListRenderItem } from '@shopify/flash-list';
import React, { memo, useCallback, useMemo } from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';
import { SCREEN_SIZE } from '../../business/constants/screen-size';
import DataCard, { DataCardSection } from './DataCard';
import DataTable, { DataTableColumn } from './DataTable';
import SkeletonLoader from './SkeletonLoader';

interface DataListProps<T> {
  data: T[];
  keyExtractor: (item: T, index: number) => string;
  onItemPress?: (item: T) => void;

  // Table configuration for tablets
  tableColumns: DataTableColumn<T>[];
  showChevron?: boolean;

  // Card configuration for mobile
  cardSections: DataCardSection<T>[];

  // Optional style overrides
  containerStyle?: object;
  tableStyle?: object;
  cardStyle?: object;

  // Device type override (for testing)
  forceLayout?: 'table' | 'card';

  // Infinite scroll support
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  isLoading?: boolean;
  isLoadingMore?: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
}

function DataList<T>({
  data,
  keyExtractor,
  onItemPress,
  tableColumns,
  showChevron = false,
  cardSections,
  containerStyle = {},
  tableStyle = {},
  cardStyle = {},
  forceLayout,
  onEndReached,
  onEndReachedThreshold = 0.1,
  isLoading = false,
  isLoadingMore = false,
  onRefresh,
  refreshing = false,
}: DataListProps<T>) {
  // Determine if device is tablet based on screen width
  const isTablet = useMemo(() => {
    if (forceLayout) {
      return forceLayout === 'table';
    }
    const { width } = Dimensions.get('window');
    return width >= SCREEN_SIZE.SMALL;
  }, [forceLayout]);

  const combinedMobileStyle = useMemo(
    () => [styles.mobileContainer, containerStyle],
    [containerStyle]
  );

  const combinedTableStyle = useMemo(
    () => [tableStyle, containerStyle],
    [tableStyle, containerStyle]
  );

  const renderCardItem: ListRenderItem<T> = useCallback(
    ({ item }) => {
      return (
        <DataCard
          data={item}
          sections={cardSections}
          onPress={onItemPress}
          showChevron={showChevron}
          cardStyle={cardStyle}
        />
      );
    },
    [cardSections, onItemPress, showChevron, cardStyle]
  );

  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <SkeletonLoader variant={isTablet ? 'table-row' : 'patient-card'} count={1} />
      </View>
    );
  }, [isLoadingMore, isTablet]);

  // Render loading state
  if (isLoading && data.length === 0) {
    return (
      <View style={[styles.container, containerStyle]}>
        <SkeletonLoader variant={isTablet ? 'table-row' : 'patient-card'} count={5} />
      </View>
    );
  }

  if (isTablet) {
    // Tablet layout - use DataTable
    return (
      <DataTable
        data={data}
        columns={tableColumns}
        keyExtractor={keyExtractor}
        onRowPress={onItemPress}
        showChevron={showChevron}
        containerStyle={combinedTableStyle}
        onEndReached={onEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        isLoadingMore={isLoadingMore}
        onRefresh={onRefresh}
        refreshing={refreshing}
      />
    );
  }

  return (
    <View style={combinedMobileStyle}>
      <FlashList
        data={data}
        keyExtractor={keyExtractor}
        renderItem={renderCardItem}
        estimatedItemSize={120}
        contentContainerStyle={styles.cardsContainer}
        showsVerticalScrollIndicator={false}
        onEndReached={onEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        ListFooterComponent={renderFooter}
        onRefresh={onRefresh}
        refreshing={refreshing}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    padding: 16,
  },
  mobileContainer: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  cardsContainer: {
    paddingVertical: 8,
  },
  footerLoader: {
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
});

// Add deep comparison for memo to prevent unnecessary re-renders
const areEqual = <T,>(prevProps: DataListProps<T>, nextProps: DataListProps<T>): boolean => {
  // Compare primitive props
  if (
    prevProps.showChevron !== nextProps.showChevron ||
    prevProps.forceLayout !== nextProps.forceLayout ||
    prevProps.onEndReachedThreshold !== nextProps.onEndReachedThreshold ||
    prevProps.isLoading !== nextProps.isLoading ||
    prevProps.isLoadingMore !== nextProps.isLoadingMore ||
    prevProps.refreshing !== nextProps.refreshing ||
    prevProps.onItemPress !== nextProps.onItemPress ||
    prevProps.onEndReached !== nextProps.onEndReached ||
    prevProps.onRefresh !== nextProps.onRefresh ||
    prevProps.keyExtractor !== nextProps.keyExtractor
  ) {
    return false;
  }

  // Compare data array length and reference
  if (prevProps.data.length !== nextProps.data.length || prevProps.data !== nextProps.data) {
    return false;
  }

  // Compare sections and columns arrays (shallow comparison should be sufficient for stable configs)
  if (
    prevProps.cardSections !== nextProps.cardSections ||
    prevProps.tableColumns !== nextProps.tableColumns
  ) {
    return false;
  }

  return true;
};

export default memo(DataList, areEqual) as <T>(props: DataListProps<T>) => React.ReactElement;
