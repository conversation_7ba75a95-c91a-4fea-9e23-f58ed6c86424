import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { memo, useMemo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { DataCardSection } from '../../../shared/presentation/components/DataCard';
import DataList from '../../../shared/presentation/components/DataList';
import { DataTableColumn } from '../../../shared/presentation/components/DataTable';
import { Facility } from '../../business/types/facility-response';
import { useFacilityInfo } from '../hooks/use-facility-formatted-info';

interface Props {
  facilities: Facility[];
  isLoading?: boolean;
  // Support for future enhancements
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  isLoadingMore?: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
}

const FacilityList = memo<Props>(
  ({
    facilities,
    isLoading = false,
    onEndReached,
    onEndReachedThreshold,
    isLoadingMore,
    onRefresh,
    refreshing,
  }) => {
    const router = useRouter();

    const handleFacilityPress = (facility: Facility) => {
      router.push({
        pathname: '/(patients)' as any,
        params: {
          facilityId: facility.id,
          facilityName: facility.name,
        },
      });
    };

    // Render patients info with icon
    const renderPatientsInfo = (facility: Facility) => {
      const { numberOrPatients } = useFacilityInfo(facility);
      return (
        <View style={styles.infoContainer}>
          <Ionicons name="person-outline" size={16} color="#6B7280" />
          <Text style={styles.infoText}>{numberOrPatients}</Text>
        </View>
      );
    };

    // Render devices info with icon
    const renderDevicesInfo = (facility: Facility) => {
      const { numberOfDevices } = useFacilityInfo(facility);
      return (
        <View style={styles.infoContainer}>
          <Ionicons name="phone-portrait-outline" size={16} color="#6B7280" />
          <Text style={styles.infoText}>{numberOfDevices}</Text>
        </View>
      );
    };

    // Render alerts info with icon
    const renderAlertsInfo = (facility: Facility) => {
      const { numberOfAlerts } = useFacilityInfo(facility);
      return (
        <View style={styles.infoContainer}>
          <Ionicons name="alert-circle-outline" size={16} color="#FFA500" />
          <Text style={styles.infoText}>{numberOfAlerts}</Text>
        </View>
      );
    };

    // Table columns configuration for tablets
    const tableColumns = useMemo<DataTableColumn<Facility>[]>(
      () => [
        {
          key: 'srNo',
          header: 'SR. NO.',
          width: 40,
          align: 'center',
          render: (facility) => {
            const { srNo } = useFacilityInfo(facility);
            return <Text style={styles.srNoText}>{srNo}</Text>;
          },
        },
        {
          key: 'name',
          header: 'FACILITY NAME',
          width: 90,
          align: 'left',
          render: (facility) => {
            const { name } = useFacilityInfo(facility);
            return (
              <Text style={styles.facilityNameText} numberOfLines={3} ellipsizeMode="tail">
                {name}
              </Text>
            );
          },
        },
        {
          key: 'patients',
          header: 'PATIENTS',
          width: 50,
          align: 'center',
          render: (facility) => {
            const { numberOrPatients } = useFacilityInfo(facility);
            return <Text style={styles.numberText}>{numberOrPatients}</Text>;
          },
        },
        {
          key: 'devices',
          header: 'DEVICES',
          width: 50,
          align: 'center',
          render: (facility) => {
            const { numberOfDevices } = useFacilityInfo(facility);
            return <Text style={styles.numberText}>{numberOfDevices}</Text>;
          },
        },
        {
          key: 'alerts',
          header: 'ALERTS',
          width: 60,
          align: 'center',
          render: (facility) => {
            const { numberOfAlerts } = useFacilityInfo(facility);
            return <Text style={styles.numberText}>{numberOfAlerts}</Text>;
          },
        },
        {
          key: 'address',
          header: 'ADDRESS',
          width: 100,
          align: 'left',
          render: (facility) => {
            const { address } = useFacilityInfo(facility);
            return (
              <Text style={styles.addressText} numberOfLines={3} ellipsizeMode="tail">
                {address || 'Address can write here.'}
              </Text>
            );
          },
        },
      ],
      []
    );

    // Card sections configuration for mobile
    const cardSections = useMemo<DataCardSection<Facility>[]>(
      () => [
        {
          type: 'header',
          fields: [
            {
              key: 'name',
              style: 'header',
              hideLabel: true,
              render: (facility) => {
                const { name } = useFacilityInfo(facility);
                return name;
              },
            },
            {
              key: 'address',
              style: 'subtitle',
              hideLabel: true,
              render: (facility) => {
                const { address } = useFacilityInfo(facility);
                return address || 'Address can write here.';
              },
            },
          ],
        },
        {
          type: 'footer',
          fields: [
            {
              key: 'patients-info',
              hideLabel: true,
              render: renderPatientsInfo,
            },
            {
              key: 'devices-info',
              hideLabel: true,
              render: renderDevicesInfo,
            },
            {
              key: 'alerts-info',
              hideLabel: true,
              render: renderAlertsInfo,
            },
          ],
        },
      ],
      []
    );

    const keyExtractor = (facility: Facility) => facility.id;

    return (
      <DataList
        data={facilities}
        keyExtractor={keyExtractor}
        onItemPress={handleFacilityPress}
        tableColumns={tableColumns}
        cardSections={cardSections}
        showChevron={true}
        isLoading={isLoading}
        onEndReached={onEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        isLoadingMore={isLoadingMore}
        onRefresh={onRefresh}
        refreshing={refreshing}
      />
    );
  }
);

FacilityList.displayName = 'FacilityList';

const styles = StyleSheet.create({
  srNoText: {
    fontSize: 7,
    fontWeight: '400',
    color: '#212529',
    textAlign: 'center',
  },
  facilityNameText: {
    fontSize: 7,
    fontWeight: '500',
    color: '#212529',
  },
  numberText: {
    fontSize: 7,
    fontWeight: '400',
    color: '#212529',
    textAlign: 'center',
  },
  addressText: {
    fontSize: 7,
    fontWeight: '400',
    color: '#495057',
    lineHeight: 14,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  infoText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#374151',
    marginLeft: 4,
  },
});

export default FacilityList;
