You are an expert in TypeScript, React Native, Expo, Domain-Driven Design (DDD), and Mobile UI development.

## Project Architecture - Domain-Driven Design (DDD)

### Folder Structure:
```
src/
├── shared/
│   ├── components/     # Reusable UI components
│   ├── types/         # Shared TypeScript types and interfaces
│   ├── utils/         # Utility functions and helpers
│   ├── constants/     # App-wide constants
│   └── hooks/         # Custom hooks
├── [domain]/          # e.g., dashboard, facilities, reporting
│   ├── presentation/
│   │   ├── screens/   # Screen components (e.g., DashboardView.tsx)
│   │   ├── components/# Domain-specific UI components
│   │   └── hooks/     # Presentation layer hooks
│   ├── business/
│   │   ├── entities/  # Business entities and models
│   │   ├── services/  # Business logic services
│   │   └── usecases/  # Use case implementations
│   ├── integration/
│   │   ├── api/       # API calls and data fetching
│   │   ├── storage/   # Local storage operations
│   │   └── adapters/  # External service adapters
│   └── capabilities/
│       ├── types/     # Domain-specific types
│       ├── interfaces/# Domain interfaces
│       └── constants/ # Domain constants
app/                   # Expo Router files (routes only)
```

### DDD Layer Responsibilities:
- **Presentation**: UI components, screens, and user interaction logic
- **Business**: Core business logic, entities, and use cases
- **Integration**: External service integration, APIs, and data persistence
- **Capabilities**: Domain-specific types, interfaces, and contracts

## Code Style and Structure

### TypeScript Usage:
- Use TypeScript for all files (.ts, .tsx)
- Define strict types for all props, state, and function parameters
- Use interfaces for object shapes and contracts
- Use type unions for specific value sets
- Avoid `any` type; use `unknown` if necessary
- Use generic types for reusable components and functions

### Component Structure:
- **ALWAYS use functional components** - Class components are forbidden except for legacy code maintenance
- For error boundaries, use `react-error-boundary` library instead of class components
- Use functional components with TypeScript interfaces for props
- Implement proper type definitions for all props
- Use React.FC<PropsType> or explicit return types
- Export types alongside components

### Naming Conventions:
- **Files & Directories**: Use PascalCase for components (UserProfile.tsx), camelCase for utilities (userService.ts)
- **Components**: PascalCase (UserProfile, DashboardView)
- **Variables & Functions**: camelCase (isLoading, handleSubmit)
- **Types & Interfaces**: PascalCase with descriptive names (UserData, ApiResponse)
- **Constants**: UPPER_SNAKE_CASE (API_BASE_URL, DEFAULT_TIMEOUT)
- **Directories**: kebab-case for multi-word domains (user-management, facility-monitoring)

### Domain Module Organization:
- Each domain should be self-contained with minimal external dependencies
- Cross-domain communication through well-defined interfaces
- Shared utilities and components in the `/shared` directory
- Domain-specific types and interfaces within each domain's capabilities folder

## TypeScript Best Practices

### Type Definitions:
- Define interfaces for component props, API responses, and business entities
- Use union types for state management (loading | success | error)
- Implement proper error handling with typed exceptions
- Use enums for fixed value sets
- Leverage utility types (Partial, Pick, Omit, etc.)

### Import/Export Patterns

#### ❌ AVOID: Index Pattern (Barrel Exports)
- Do NOT create `index.ts` files for re-exporting components or modules
- Do NOT use barrel exports that re-export from multiple files
- Reason: Causes circular dependencies, makes dependency tracking difficult, and can lead to require cycle warnings

#### ✅ PREFER: Direct Imports
- Import directly from the source file where the component/function is defined
- Use explicit import paths to make dependencies clear and traceable

#### Examples:

**❌ BAD - Index Pattern:**
```typescript
// components/index.ts
export { UserProfile } from './UserProfile';
export { UserList } from './UserList';

// SomeScreen.tsx
import { UserProfile, UserList } from './components'; // Avoid this
```

**✅ GOOD - Direct Imports:**
```typescript
// SomeScreen.tsx
import { UserProfile } from './components/UserProfile';
import { UserList } from './components/UserList';
```

#### Exception: Shared Types and Constants
- Only use index files for pure type definitions and constants that don't import other modules
- These should only contain type exports, interfaces, and constants - no component re-exports

#### Example of acceptable index usage:
```typescript
// types/index.ts - ONLY types and constants
export type { User, UserRole } from './user-types';
export type { ApiResponse } from './api-types';
export const API_ENDPOINTS = { ... };
```

## Architecture Guidelines
- Follow DDD structure: each domain should have clear boundaries
- Keep imports within the same layer when possible
- Cross-layer imports should follow dependency direction: Presentation → Business → Integration
- Avoid circular dependencies between any modules

## File Organization
- Use descriptive file names that clearly indicate their purpose
- Place files in appropriate domain folders following DDD structure
- Keep related functionality close together within the same domain

## Component Exports
- Export components as named exports when possible
- Use default exports sparingly, only for main screen components
- Always include proper TypeScript types for all exports

## React Native & Expo Optimization

### Performance:
- Use React.memo() for components that receive stable props
- Implement useCallback() and useMemo() for expensive computations
- Optimize FlatList with proper keyExtractor, getItemLayout, and removeClippedSubviews
- Use lazy loading for screens and components
- Implement proper image optimization strategies

### State Management:
- Use local state (useState) for component-specific data
- Implement Context API for domain-specific shared state
- Consider Zustand or Redux Toolkit for complex global state
- Keep state as close to where it's used as possible

### Navigation with Expo Router:
- Organize routes in `/app` directory following file-based routing
- Use TypeScript for route parameters and navigation props
- Implement proper deep linking with type safety
- Use layout files for shared navigation structure

## UI and Styling

### Styling Approach:
- Use StyleSheet.create() for static styles
- Implement responsive design using Dimensions and device info
- Create a consistent design system with shared theme
- Use meaningful style names that describe purpose, not appearance

### Component Design:
- Build reusable components in `/shared/components`
- Implement proper TypeScript interfaces for component APIs
- Use composition over inheritance for component relationships
- Follow atomic design principles (atoms, molecules, organisms)

## Business Logic Organization

### Service Layer:
- Implement business services in `/business/services`
- Define clear interfaces for external dependencies
- Use dependency injection patterns where appropriate
- Keep business logic framework-agnostic

### Use Cases:
- Implement specific use cases in `/business/usecases`
- Each use case should handle a single business operation
- Define clear input/output types for each use case
- Handle errors at the use case level

### Entity Management:
- Define business entities in `/business/entities`
- Implement proper validation and business rules
- Use immutable data patterns where possible
- Separate data models from UI models

## Integration Layer

### API Integration:
- Implement API clients in `/integration/api`
- Use proper error handling and retry mechanisms
- Define clear response and request types
- Implement proper loading and error states

### Data Persistence:
- Handle local storage in `/integration/storage`
- Implement proper data serialization/deserialization
- Use appropriate storage solutions (AsyncStorage, SQLite, etc.)
- Handle storage errors gracefully

## Testing Strategy

### Unit Testing:
- Test business logic and utility functions
- Mock external dependencies in tests
- Use Jest with TypeScript configuration
- Implement proper test coverage for critical paths

### Component Testing:
- Use React Native Testing Library
- Test component behavior, not implementation details
- Mock navigation and external services
- Test accessibility features

## Error Handling

### Error Management:
- Implement proper error boundaries for React components
- Define custom error types for different failure scenarios
- Use Result/Either patterns for error handling in business logic
- Provide meaningful error messages to users

### Logging and Monitoring:
- Implement structured logging throughout the application
- Use proper log levels (debug, info, warn, error)
- Consider crash reporting tools (Sentry, Bugsnag)
- Monitor performance metrics and user interactions

## Security Best Practices

### Data Protection:
- Validate all user inputs at boundaries
- Implement proper authentication and authorization
- Use secure storage for sensitive data
- Follow OWASP mobile security guidelines

### API Security:
- Implement proper API authentication
- Use HTTPS for all network communications
- Handle sensitive data appropriately
- Implement proper session management

Remember: Always prioritize code readability, maintainability, and type safety. Use the DDD structure to keep business logic separate from presentation concerns, and leverage TypeScript's type system to catch errors early in development. 