import { inject, injectable } from 'inversify';
import { CORE_DEPENDENCY_INJECTION_TYPES } from '../../../core/dependency-injection/types';
import { AxiosService } from '../../../core/services/axios.service';
import { IPatientService } from '../../business/api/patient.service';
import { PatientResponse } from '../../business/types/patient-response';
import { PatientListParams } from '../../business/types/patient-list-params';

@injectable()
export class PatientService implements IPatientService {
  constructor(
    @inject(CORE_DEPENDENCY_INJECTION_TYPES.AxiosService)
    private readonly axiosService: AxiosService
  ) {}
  async getPatients({ filters }: PatientListParams): Promise<PatientResponse> {
    try {
      const params = filters?.reduce(
        (acc, filter) => {
          if ('field' in filter && 'value' in filter) {
            acc[filter.field] = filter.value;
          }
          return acc;
        },
        {} as Record<string, any>
      );
      const response = await this.axiosService.axios.get<PatientResponse>('/patients', { params });
      return response.data;
    } catch (error) {
      console.log(error);
      return Promise.resolve({
        data: [],
        pagination: { current_page: 1, total_pages: 1, total_count: 0 },
        success: false,
      });
    }
  }
}
