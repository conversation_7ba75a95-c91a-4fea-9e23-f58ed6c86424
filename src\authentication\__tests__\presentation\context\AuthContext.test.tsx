// Mock all external dependencies first, before any imports
jest.mock('../../../../shared/presentation/hooks/use-storage-state');
jest.mock('../../../../core/hooks/use-dependency-injection');
jest.mock('../../../integration/register-dependency-injection', () => ({
  authContainer: {},
}));
jest.mock('../../../business/constants', () => ({
  AUTH_TOKEN_KEY: 'auth_token',
}));

import { useSession } from '../../../presentation/context/AuthContext';

const mockSetSession = jest.fn();
const mockAuthService = {
  login: jest.fn(),
  checkToken: jest.fn(),
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    const { useStorageState } = require('../../../../shared/presentation/hooks/use-storage-state');
    const { useDependencyInjection } = require('../../../../core/hooks/use-dependency-injection');

    useStorageState.mockReturnValue([[false, null], mockSetSession]);
    useDependencyInjection.mockReturnValue(mockAuthService);
  });

  describe('useSession', () => {
    it('should be a function', () => {
      expect(typeof useSession).toBe('function');
    });
  });

  describe('Authentication Logic', () => {
    it('should handle successful login', async () => {
      const mockToken = 'login-success-token';
      mockAuthService.login.mockResolvedValue({ data: { token: mockToken } });

      const result = await mockAuthService.login('<EMAIL>', 'password123');

      expect(mockAuthService.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(result.data.token).toBe(mockToken);
    });

    it('should handle failed login with empty token', async () => {
      mockAuthService.login.mockResolvedValue({ data: { token: '' } });

      const result = await mockAuthService.login('<EMAIL>', 'wrongpassword');

      expect(mockAuthService.login).toHaveBeenCalledWith('<EMAIL>', 'wrongpassword');
      expect(result.data.token).toBe('');
    });

    it('should handle token validation for valid tokens', async () => {
      mockAuthService.checkToken.mockResolvedValue({ data: { isTokenValid: true } });

      const result = await mockAuthService.checkToken('valid-token');

      expect(mockAuthService.checkToken).toHaveBeenCalledWith('valid-token');
      expect(result.data.isTokenValid).toBe(true);
    });

    it('should handle token validation for invalid tokens', async () => {
      mockAuthService.checkToken.mockResolvedValue({ data: { isTokenValid: false } });

      const result = await mockAuthService.checkToken('invalid-token');

      expect(mockAuthService.checkToken).toHaveBeenCalledWith('invalid-token');
      expect(result.data.isTokenValid).toBe(false);
    });
  });

  describe('Storage State Integration', () => {
    it('should use storage state hook with correct behavior', () => {
      const {
        useStorageState,
      } = require('../../../../shared/presentation/hooks/use-storage-state');

      // Verify the hook is being used (mocked)
      expect(useStorageState).toBeDefined();
    });

    it('should handle loading state correctly', () => {
      const {
        useStorageState,
      } = require('../../../../shared/presentation/hooks/use-storage-state');

      // Test loading state
      useStorageState.mockReturnValue([[true, null], mockSetSession]);
      const [state, setState] = useStorageState('auth_token');

      expect(state[0]).toBe(true); // isLoading
      expect(state[1]).toBe(null); // session
      expect(typeof setState).toBe('function');
    });

    it('should handle session state correctly', () => {
      const {
        useStorageState,
      } = require('../../../../shared/presentation/hooks/use-storage-state');

      const mockToken = 'stored-token';
      useStorageState.mockReturnValue([[false, mockToken], mockSetSession]);
      const [state, setState] = useStorageState('auth_token');

      expect(state[0]).toBe(false); // isLoading
      expect(state[1]).toBe(mockToken); // session
      expect(typeof setState).toBe('function');
    });

    it('should provide signOut functionality', () => {
      // Test that the signOut functionality exists and can be called
      expect(mockSetSession).toBeDefined();
      expect(typeof mockSetSession).toBe('function');

      // Call signOut equivalent
      mockSetSession(null);
      expect(mockSetSession).toHaveBeenCalledWith(null);
    });
  });

  describe('Dependency Injection Integration', () => {
    it('should use dependency injection to get auth service', () => {
      const { useDependencyInjection } = require('../../../../core/hooks/use-dependency-injection');

      expect(useDependencyInjection).toBeDefined();

      const service = useDependencyInjection();
      expect(service).toBe(mockAuthService);
      expect(service.login).toBeDefined();
      expect(service.checkToken).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle login service errors', async () => {
      const loginError = new Error('Network error');
      mockAuthService.login.mockRejectedValue(loginError);

      try {
        await mockAuthService.login('<EMAIL>', 'password123');
      } catch (error) {
        expect(error).toBe(loginError);
        expect(mockAuthService.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      }
    });

    it('should handle token validation errors', async () => {
      const tokenError = new Error('Token validation failed');
      mockAuthService.checkToken.mockRejectedValue(tokenError);

      try {
        await mockAuthService.checkToken('problematic-token');
      } catch (error) {
        expect(error).toBe(tokenError);
        expect(mockAuthService.checkToken).toHaveBeenCalledWith('problematic-token');
      }
    });
  });

  describe('Context Behavior', () => {
    it('should provide checkToken functionality', async () => {
      mockAuthService.checkToken.mockResolvedValue({ data: { isTokenValid: true } });

      const result = await mockAuthService.checkToken('test-token');

      expect(mockAuthService.checkToken).toHaveBeenCalledWith('test-token');
      expect(result).toEqual({ data: { isTokenValid: true } });
    });

    it('should provide signIn functionality that calls auth service', async () => {
      const mockToken = 'test-token-123';
      mockAuthService.login.mockResolvedValue({ data: { token: mockToken } });

      // Test the auth service functionality directly
      expect(mockAuthService.login).toBeDefined();
      expect(typeof mockAuthService.login).toBe('function');

      const result = await mockAuthService.login('<EMAIL>', 'password123');
      expect(result.data.token).toBe(mockToken);
    });
  });
});
