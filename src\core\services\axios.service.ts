import type { AxiosInstance } from 'axios';
import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { injectable } from 'inversify';
import { Platform } from 'react-native';
import { AUTH_TOKEN_KEY } from '../../authentication/business/constants';

// Utility function to get token from storage (similar to setStorageItemAsync pattern)
async function getStorageToken(): Promise<string | null> {
  if (Platform.OS === 'web') {
    try {
      if (typeof localStorage !== 'undefined') {
        return localStorage.getItem(AUTH_TOKEN_KEY);
      }
    } catch (e) {
      console.error('Local storage is unavailable:', e);
    }
    return null;
  } else {
    try {
      return await SecureStore.getItemAsync(AUTH_TOKEN_KEY);
    } catch (e) {
      console.error('SecureStore is unavailable:', e);
      return null;
    }
  }
}

@injectable()
class AxiosService {
  private axiosInstance: AxiosInstance;
  private baseURL = 'https://staging-api.moxie-link.com/rpm';

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.axiosInstance.interceptors.request.use(
      async (config) => {
        // Get token from storage (localStorage for web, SecureStore for mobile)
        const token = await getStorageToken();

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        console.error('Axios Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Interceptor for handling errors globally
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        if (error.response?.status === 401 && !originalRequest._retry) {
          // delete token from storage
          await SecureStore.deleteItemAsync(AUTH_TOKEN_KEY);
        }
        console.log('Axios Error Details:', {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          config: {
            url: error.config?.url,
            method: error.config?.method,
            baseURL: error.config?.baseURL,
          },
        });
        return Promise.reject(error);
      }
    );
  }

  get axios() {
    return this.axiosInstance;
  }
}

export { AxiosService };
