import { COLORS } from '@/src/shared/business/constants/colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useMemo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { DataCardSection } from '../../../shared/presentation/components/DataCard';
import DataList from '../../../shared/presentation/components/DataList';
import { DataTableColumn } from '../../../shared/presentation/components/DataTable';
import { IPatient } from '../../business/types/patient-response';

interface Props {
  patients: IPatient[];
  onPatientPress?: (patient: IPatient) => void;
  // Infinite scroll support
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  isLoading?: boolean;
  isLoadingMore?: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
}

const PatientList: React.FC<Props> = ({
  patients,
  onPatientPress,
  onEndReached,
  onEndReachedThreshold,
  isLoading,
  isLoadingMore,
  onRefresh,
  refreshing,
}) => {
  const renderStatusBadge = (patient: IPatient) => {
    const isActive = patient.status === 'Active';
    return (
      <View style={[styles.statusBadge, isActive ? styles.activeBadge : styles.inactiveBadge]}>
        <Text style={styles.statusText}>{patient.status}</Text>
      </View>
    );
  };

  // Alerts renderer for cards
  const renderAlertsSection = (patient: IPatient) => {
    return (
      <View style={styles.alertsContainer}>
        <Ionicons name="warning" size={16} color="#FFA500" />
        <Text style={styles.alertsText}>{patient.alerts}</Text>
      </View>
    );
  };

  // Table columns configuration for tablets
  const tableColumns = useMemo<DataTableColumn<IPatient>[]>(
    () => [
      {
        key: 'name',
        header: 'PATIENT NAME',
        width: 85,
        align: 'left',
      },
      {
        key: 'dob',
        header: 'DOB',
        width: 70,
        align: 'center',
      },
      {
        key: 'age',
        header: 'AGE (YR)',
        width: 40,
        align: 'center',
      },
      {
        key: 'room',
        header: 'ROOM',
        width: 40,
        align: 'center',
      },
      {
        key: 'icd',
        header: 'ICD',
        width: 40,
        align: 'left',
      },
      {
        key: 'alerts',
        header: 'ALERTS',
        width: 40,
        align: 'center',
        render: (patient) => {
          return <Text style={styles.alertsTextTable}>{patient.alerts ?? 'N/A'}</Text>;
        },
      },
      {
        key: 'custom',
        header: 'STATUS',
        width: 70,
        align: 'center',
        render: renderStatusBadge,
      },
    ],
    []
  );

  // Card sections configuration for mobile
  const cardSections = useMemo<DataCardSection<IPatient>[]>(
    () => [
      {
        type: 'header',
        fields: [
          {
            key: 'name',
            style: 'header',
            hideLabel: true,
          },
          {
            key: 'patient-info',
            style: 'subtitle',
            hideLabel: true,
            render: (patient) => `${patient.dob}, ${patient.age}yo`,
          },
        ],
      },
      {
        type: 'info',
        fields: [
          {
            key: 'room',
            label: 'Room',
          },
          {
            key: 'icd',
            hideLabel: true,
            style: 'body',
          },
        ],
      },
      {
        type: 'footer',
        fields: [
          {
            key: 'status-badge',
            hideLabel: true,
            render: renderStatusBadge,
          },
          {
            key: 'alerts-section',
            hideLabel: true,
            render: renderAlertsSection,
          },
        ],
      },
    ],
    []
  );

  const keyExtractor = (patient: IPatient) => patient.id;

  return (
    <DataList
      data={patients}
      keyExtractor={keyExtractor}
      onItemPress={onPatientPress}
      tableColumns={tableColumns}
      cardSections={cardSections}
      showChevron={true}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      isLoading={isLoading}
      isLoadingMore={isLoadingMore}
      onRefresh={onRefresh}
      refreshing={refreshing}
    />
  );
};

const styles = StyleSheet.create({
  statusBadge: {
    fontSize: 10,
    fontFamily: 'DMSans-Regular',
    fontWeight: '500',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    alignItems: 'center',
  },
  activeBadge: {
    backgroundColor: '#10B981',
  },
  inactiveBadge: {
    backgroundColor: '#EF4444',
  },
  statusText: {
    fontSize: 7,
    fontFamily: 'DMSans-Regular',
    fontWeight: '400',
    color: COLORS.WHITE,
    textAlign: 'center',
  },
  alertsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  alertsText: {
    fontSize: 10,
    fontWeight: '500',
    color: '#1F2937',
    marginLeft: 4,
  },
  alertsTextTable: {
    fontSize: 7,
    fontFamily: 'DMSans-Regular',
    fontWeight: '500',
    color: COLORS.TEXT_MAIN,
    textAlign: 'center',
  },
});

export default PatientList;
// export default memo(PatientList);
