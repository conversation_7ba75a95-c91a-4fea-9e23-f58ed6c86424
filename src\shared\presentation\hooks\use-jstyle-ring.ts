import {
  SMART_RING_COMMANDS,
  SMART_RING_SERVICE_UUID,
} from '@/src/shared/business/constants/ble-constants';
import * as ExpoDevice from 'expo-device';
import { useCallback, useMemo, useRef, useState } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import base64 from 'react-native-base64';
import { BleError, BleManager, Characteristic, Device, Subscription } from 'react-native-ble-plx';

// JUMPER BLUETOOTH SERVICE UUIDS
const SERVICE_UUID = 'CDEACB80-5235-4C07-8846-93A37EE6B86D';
const TX_CHARACTERISTIC = 'CDEACB82-5235-4C07-8846-93A37EE6B86D'; // WRITE
const RX_CHARACTERISTIC = 'CDEACB81-5235-4C07-8846-93A37EE6B86D'; // READ

interface HealthData {
  heartRate: number;
  spo2: number;
}

export const useJStyleRing = () => {
  const bleManager = useMemo(() => new BleManager(), []);
  const [allDevices, setAllDevices] = useState<Device[]>([]);
  const [connectedDevice, setConnectedDevice] = useState<Device | null>(null);
  const [healthData, setHealthData] = useState<HealthData | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isMeasuring, setIsMeasuring] = useState(false);
  const [measurementType, setMeasurementType] = useState<'heart_rate' | 'spo2' | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const monitorRef = useRef<Subscription | null>(null);

  const stopScan = useCallback(() => {
    bleManager.stopDeviceScan();
    setIsScanning(false);
  }, [bleManager]);

  // Convert Uint8Array to base64 for BLE transmission
  const toBase64 = useCallback((data: Uint8Array): string => {
    return base64.encodeFromByteArray(data);
  }, []);

  // Add the actual MeasurementWithType command constant
  const CMD_MEASUREMENT_WITH_TYPE = 0x86; // Based on the Java SDK analysis

  // Parse health measurement response
  const parseHealthData = useCallback((data: string): HealthData | null => {
    try {
      const bytes = base64.decode(data);
      const byteArray = Array.from(bytes).map((c) => c.charCodeAt(0));
      console.log(
        'Raw bytes received:',
        byteArray.map((b) => b.toString(16).padStart(2, '0')).join(' ')
      );

      const command = bytes.charCodeAt(0);
      console.log('Command received:', command.toString(16));

      if (command === CMD_HEALTH_MEASUREMENT) {
        // Health measurement response (0x28)
        // According to PDF: "Returns every second: 0x28 AABB CC DD EE FF GG 00 00 00 00 00 00 00 CRC"
        const measurementType = bytes.charCodeAt(1);
        console.log('Health measurement type:', measurementType);

        const healthData = {
          heartRate: bytes.charCodeAt(2), // BB: heart rate value
          spo2: bytes.charCodeAt(3), // CC: blood oxygen value
          hrv: bytes.charCodeAt(4), // DD: HRV value
          fatigue: bytes.charCodeAt(5), // EE: fatigue level
          highBP: bytes.charCodeAt(6), // FF: high blood pressure
          lowBP: bytes.charCodeAt(7), // GG: hypotension
          timestamp: new Date(),
        };

        console.log('Parsed 0x28 health data:', healthData);
        return healthData;
      } else if (command === CMD_MEASUREMENT_WITH_TYPE) {
        // 0x86
        // MeasurementWithType response - this is the ACTUAL command from JStyle SDK!
        console.log('✅ MeasurementWithType response (0x86) - This is the correct JStyle command');

        const measurementType = bytes.charCodeAt(1);
        console.log('Measurement type from SDK pattern:', measurementType);

        // Based on the Java SDK, the data extraction is:
        // value[2] = HeartRate
        // value[3] = Blood_oxygen
        // value[4] = HRV
        // value[5] = Stress/Fatigue
        // value[6] = HighPressure
        // value[7] = LowPressure

        const healthData = {
          heartRate: bytes.charCodeAt(2), // Same as SDK: value[2]
          spo2: bytes.charCodeAt(3), // Same as SDK: value[3]
          hrv: bytes.charCodeAt(4), // Same as SDK: value[4]
          fatigue: bytes.charCodeAt(5), // Same as SDK: value[5] (Stress)
          highBP: bytes.charCodeAt(6), // Same as SDK: value[6]
          lowBP: bytes.charCodeAt(7), // Same as SDK: value[7]
          timestamp: new Date(),
        };

        console.log('✅ Parsed 0x86 data using JStyle SDK pattern:', healthData);

        // Check if we got valid data (non-zero values)
        if (healthData.heartRate > 0 || healthData.spo2 > 0 || healthData.hrv > 0) {
          console.log('✅ Valid measurement data received!');
          return healthData;
        } else {
          console.log('⏳ Measurement in progress - received zeros (device still measuring)');
          return healthData; // Return even if zeros to show measurement is active
        }
      } else if (command === 0x56) {
        // HRV test data response (0x56) - documented in PDF
        console.log('HRV test data received - command 0x56 (documented)');

        if (bytes.length >= 15) {
          const healthData = {
            heartRate: bytes.charCodeAt(11), // D3: Heart rate value
            spo2: 0, // Not provided in HRV data
            hrv: bytes.charCodeAt(9), // D1: HRV value
            fatigue: bytes.charCodeAt(12), // D4: Fatigue
            highBP: bytes.charCodeAt(13), // P1: High blood pressure
            lowBP: bytes.charCodeAt(14), // P2: Low blood pressure
            timestamp: new Date(),
          };

          console.log('Parsed 0x56 HRV data:', healthData);
          return healthData;
        }
      } else if (command === CMD_REAL_TIME_STEP_COUNTING) {
        console.log('Real-time step counting data received - command 0x09 (documented)');
        const heartRate = extractHeartRate(data);
        return {
          fatigue: 0,
          highBP: 0,
          lowBP: 0,
          hrv: 0,
          heartRate: heartRate ?? 0,
          spo2: 0,
          timestamp: new Date(),
        };
      } else {
        console.log('Unknown command received:', command.toString(16));
      }

      return null;
    } catch (error) {
      console.error('Error parsing health data:', error);
      return null;
    }
  }, []);

  // Request Android permissions
  const requestAndroid31Permissions = async () => {
    const bluetoothScanPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
      {
        title: 'Bluetooth Scan Permission',
        message: 'This app needs Bluetooth scan permission to discover devices',
        buttonPositive: 'OK',
      }
    );
    const bluetoothConnectPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
      {
        title: 'Bluetooth Connect Permission',
        message: 'This app needs Bluetooth connect permission to connect to devices',
        buttonPositive: 'OK',
      }
    );
    const fineLocationPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: 'Location Permission',
        message: 'Bluetooth Low Energy requires Location permission to scan for devices',
        buttonPositive: 'OK',
      }
    );

    const allGranted =
      bluetoothScanPermission === 'granted' &&
      bluetoothConnectPermission === 'granted' &&
      fineLocationPermission === 'granted';

    return {
      granted: allGranted,
      message: !allGranted ? 'One or more Bluetooth permissions were denied' : undefined,
    };
  };

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      if ((ExpoDevice.platformApiLevel ?? -1) < 31) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'Bluetooth Low Energy requires Location permission to scan for devices',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        const result = await requestAndroid31Permissions();
        return result.granted;
      }
    } else {
      return true;
    }
  };

  const isDuplicateDevice = (devices: Device[], nextDevice: Device) =>
    devices.findIndex((device) => nextDevice.id === device.id) > -1;

  const scanForRing = useCallback(() => {
    if (isScanning) return;

    setIsScanning(true);
    setAllDevices([]);

    bleManager.startDeviceScan(null, null, (error, device) => {
      if (error) {
        console.error('Scan error:', error);
        setIsScanning(false);
        return;
      }

      if (device && device.name) {
        setAllDevices((prevState: Device[]) => {
          if (!isDuplicateDevice(prevState, device)) {
            return [...prevState, device];
          }
          return prevState;
        });
      }
    });
  }, [bleManager, isScanning]);

  const connectToRing = useCallback(
    async (device: Device) => {
      try {
        setIsConnecting(true);
        const deviceConnection = await bleManager.connectToDevice(device.id);
        setConnectedDevice(deviceConnection);
        await deviceConnection.discoverAllServicesAndCharacteristics();
        bleManager.stopDeviceScan();
        setIsScanning(false);

        // Start monitoring for health data responses
        startMonitoring(deviceConnection);
      } catch (error) {
        console.error('Failed to connect to ring:', error);
      } finally {
        setIsConnecting(false);
      }
    },
    [bleManager]
  );

  const startMonitoring = useCallback(
    (device: Device) => {
      device.monitorCharacteristicForService(
        SERVICE_UUID,
        RX_CHARACTERISTIC,
        (error: BleError | null, characteristic: Characteristic | null) => {
          if (error) {
            console.error('Monitoring error:', error);
            return;
          }

          if (!characteristic?.value) {
            console.log('No data received');
            return;
          }

          const parsedData = parseHealthData(characteristic.value);
          if (parsedData) {
            setHealthData(parsedData);

            // Only update measuring state if we get actual non-zero values
            // or if this is a stop measurement response
            if (parsedData.heartRate > 0 || parsedData.spo2 > 0) {
              console.log('Received valid measurement data');
            }
          }
        }
      );
    },
    [parseHealthData]
  );

  const sendCommand = useCallback(
    async (command: Uint8Array) => {
      if (!connectedDevice) {
        throw new Error('No device connected');
      }

      try {
        await connectedDevice.writeCharacteristicWithResponseForService(
          SERVICE_UUID,
          TX_CHARACTERISTIC,
          toBase64(command)
        );
      } catch (error) {
        console.error('Failed to send command:', error);
        throw error;
      }
    },
    [connectedDevice, toBase64]
  );

  const startHeartRateMeasurement = useCallback(
    async (duration: number = 30) => {
      if (!connectedDevice) {
        throw new Error('No device connected');
      }

      setIsMeasuring(true);
      setMeasurementType('heart_rate');

      // According to protocol: 0x28 AABB 00 CC DD 00 00 00 00 00 00 00 00 00 CRC
      // AA: 0x02 for heart rate measurement
      // BB: 0x01 for start measurement
      // CC DD: Test duration in seconds (low byte first)
      const durationLow = duration & 0xff;
      const durationHigh = (duration >> 8) & 0xff;

      const payload = [
        MEASUREMENT_HEART_RATE, // AA: 0x02
        0x01, // BB: Start measurement
        0x00, // Reserved
        durationLow, // CC: duration low byte
        durationHigh, // DD: duration high byte
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00, // Rest zeros
      ];

      const command = createCommand(CMD_HEALTH_MEASUREMENT, payload);
      console.log(
        'Sending heart rate command:',
        Array.from(command)
          .map((b) => b.toString(16).padStart(2, '0'))
          .join(' ')
      );

      try {
        await sendCommand(command);
        console.log('Heart rate measurement started');

        // Set timeout to automatically stop measuring and reset state
        setTimeout(
          () => {
            if (isMeasuring) {
              setIsMeasuring(false);
              setMeasurementType(null);
              console.log('Measurement timeout - automatically stopped');
            }
          },
          (duration + 5) * 1000
        ); // Add 5 seconds buffer
      } catch (error) {
        setIsMeasuring(false);
        setMeasurementType(null);
        throw error;
      }
    },
    [connectedDevice, createCommand, sendCommand, isMeasuring]
  );
  const startRealTimeStepCounting = useCallback(async () => {
    if (!connectedDevice) {
      throw new Error('No device connected');
    }
    const services = await connectedDevice.services();
    const smartRingService = services.find((service) =>
      service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
    );

    if (!smartRingService) {
      throw new Error('J Style smart ring service not available on this device');
    }

    setIsMeasuring(true);

    const command = new Uint8Array(16);
    command[0] = SMART_RING_COMMANDS.REAL_TIME_STEP_COUNTING;
    command[1] = 0x01;
    command[2] = 0x01;

    let crc = 0;
    for (let i = 0; i < 15; i++) {
      crc += command[i];
    }
    command[15] = crc & 0xff;

    const commandBase64 = base64.encode(String.fromCharCode(...command));

    await connectedDevice.writeCharacteristicWithResponseForService(
      smartRingService.uuid,
      TX_CHARACTERISTIC,
      commandBase64
    );

    monitorRef.current = connectedDevice.monitorCharacteristicForService(
      SERVICE_UUID,
      RX_CHARACTERISTIC,
      (error: BleError | null, characteristic: Characteristic | null) => {
        if (error) {
          console.error('Monitoring error:', error);
          return;
        }

        if (!characteristic?.value) {
          console.log('No data received');
          return;
        }

        const parsedData = parseHealthData(characteristic.value);
        if (parsedData) {
          setHealthData(parsedData);
        }
      }
    );
  }, [connectedDevice]);
  const startSpO2Measurement = useCallback(
    async (duration: number = 30) => {
      if (!connectedDevice) {
        throw new Error('No device connected');
      }

      setIsMeasuring(true);
      setMeasurementType('spo2');

      // According to protocol: 0x28 AABB 00 CC DD 00 00 00 00 00 00 00 00 00 CRC
      // AA: 0x03 for SpO2 measurement
      // BB: 0x01 for start measurement
      // CC DD: Test duration in seconds (low byte first)
      const durationLow = duration & 0xff;
      const durationHigh = (duration >> 8) & 0xff;

      const payload = [
        MEASUREMENT_SPO2, // AA: 0x03
        0x01, // BB: Start measurement
        0x00, // Reserved
        durationLow, // CC: duration low byte
        durationHigh, // DD: duration high byte
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00, // Rest zeros
      ];

      const command = createCommand(CMD_HEALTH_MEASUREMENT, payload);
      console.log(
        'Sending SpO2 command:',
        Array.from(command)
          .map((b) => b.toString(16).padStart(2, '0'))
          .join(' ')
      );

      try {
        await sendCommand(command);
        console.log('SpO2 measurement started');

        // Set timeout to automatically stop measuring and reset state
        setTimeout(
          () => {
            if (isMeasuring) {
              setIsMeasuring(false);
              setMeasurementType(null);
              console.log('Measurement timeout - automatically stopped');
            }
          },
          (duration + 5) * 1000
        ); // Add 5 seconds buffer
      } catch (error) {
        setIsMeasuring(false);
        setMeasurementType(null);
        throw error;
      }
    },
    [connectedDevice, createCommand, sendCommand, isMeasuring]
  );

  const stopMeasurement = useCallback(async () => {
    if (!connectedDevice || !measurementType) {
      return;
    }

    // const measurementCmd =
    //   measurementType === 'heart_rate' ? MEASUREMENT_HEART_RATE : MEASUREMENT_SPO2;

    // const payload = [
    //   measurementCmd, // Measurement type
    //   0x00, // Stop measurement
    // ];

    // const command = createCommand(CMD_HEALTH_MEASUREMENT, payload);

    // try {
    //   await sendCommand(command);
    //   setIsMeasuring(false);
    //   setMeasurementType(null);
    //   console.log('Measurement stopped');
    // } catch (error) {
    //   console.error('Failed to stop measurement:', error);
    //   throw error;
    // }
  }, [connectedDevice, measurementType]);

  const disconnectFromDevice = useCallback(() => {
    if (connectedDevice) {
      bleManager.cancelDeviceConnection(connectedDevice.id);
      setConnectedDevice(null);
      setHealthData(null);
      setIsMeasuring(false);
      setMeasurementType(null);
    }
  }, [connectedDevice, bleManager]);
  const extractBinaryData = (base64String: string): number[] => {
    const binaryString = atob(base64String);
    const bytes: number[] = [];
    for (let i = 0; i < binaryString.length; i++) {
      bytes.push(binaryString.charCodeAt(i));
    }
    return bytes;
  };

  return {
    startRealTimeStepCounting,
    stopScan,
    isConnecting,
    scanForRing,
    requestPermissions,
    connectToRing,
    allDevices,
    connectedDevice,
    disconnectFromDevice,
    startHeartRateMeasurement,
    startSpO2Measurement,
    stopMeasurement,
    healthData,
    isScanning,
    isMeasuring,
    measurementType,
  };

  //   ================================================

  //   ================================================
};

export default useJStyleRing;
