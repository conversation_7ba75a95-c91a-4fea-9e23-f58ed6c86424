import { FontAwesome, Ionicons } from '@expo/vector-icons';
import React, { useRef, useState } from 'react';
import { Animated, Easing, Pressable, StyleSheet, Text, View } from 'react-native';

export default function ProfileDropdownMenu() {
  const [isOpen, setIsOpen] = useState(true);
  const animation = useRef(new Animated.Value(0)).current;

  const toggleDropdown = () => {
    setIsOpen(!isOpen);

    Animated.timing(animation, {
      toValue: isOpen ? 0 : 1,
      duration: 250,
      useNativeDriver: false,
      easing: Easing.out(Easing.ease),
    }).start();
  };

  const heightInterpolate = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 155], // adjust height if needed
  });

  const opacityInterpolate = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  return (
    <View>
      <Pressable onPress={toggleDropdown}>
        <Ionicons name="person-circle-outline" size={32} color="#666" />
      </Pressable>

      <Animated.View
        style={[styles.menu, { height: heightInterpolate, opacity: opacityInterpolate }]}
      >
        {isOpen && (
          <>
            <Pressable style={[styles.menuItem]}>
              <FontAwesome name="user" size={25} color="#9CA3AF" />
              <Text style={styles.menuText}>Edit profile</Text>
            </Pressable>

            <Pressable style={[styles.menuItem]}>
              <FontAwesome name="lock" size={25} color="#9CA3AF" />
              <Text style={styles.menuText}>Change password</Text>
            </Pressable>

            <Pressable style={[styles.menuItem, styles.lastMenuItem]}>
              <FontAwesome name="sign-out" size={25} color="#EF4444" />
              <Text style={styles.logoutText}>Logout</Text>
            </Pressable>
          </>
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  menu: {
    top: 40,
    right: 0,
    width: 200,
    height: 200,
    borderRadius: 12,
    position: 'absolute',
    zIndex: 10,
    backgroundColor: 'white',
    paddingHorizontal: 8,
    paddingVertical: 8,
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 8,
  },
  menuItemPressed: {
    backgroundColor: '#F3F4F6',
  },
  lastMenuItem: {
    marginBottom: 0,
  },
  menuText: {
    fontSize: 16,
    color: '#2E3439',
    fontFamily: 'DMSans',
    fontWeight: '400',
  },
  logoutText: {
    fontSize: 16,
    color: '#EF4444',
    fontFamily: 'DMSans',
    fontWeight: '400',
  },
});

export { styles };
