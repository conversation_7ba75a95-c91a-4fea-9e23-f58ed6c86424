import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { SafeAreaView, ScrollView, StyleSheet, Text, View } from 'react-native';
import { AlertItem } from '../components/AlertItem';
import { Card } from '../components/Card';
import { PatientsCompliance } from '../components/PatientsCompliance';
import ProfileDropdownMenu from '../components/profileDropdown';

const DashboardView = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.greeting}>Hello, <PERSON> Deo. Good morning!</Text>
          <View style={styles.headerIcons}>
            <Ionicons name="refresh-outline" size={24} color="#666" />
            {/* <Ionicons name="person-circle-outline" size={32} color="#666" /> */}
            <ProfileDropdownMenu />
          </View>
        </View>

        {/* Facilities Summary Table */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Facilities Summary</Text>
          <View style={styles.table}>
            <View style={styles.tableHeader}>
              <Text style={[styles.tableHeaderText, { flex: 2 }]}>NAME</Text>
              <Text style={styles.tableHeaderText}>Devices</Text>
              <Text style={styles.tableHeaderText}>Alerts</Text>
              <Text style={styles.tableHeaderText}>Patients</Text>
            </View>
            {facilitiesData.map((facility, index) => (
              <View key={index} style={styles.tableRow}>
                <Text style={[styles.tableCell, { flex: 2 }]}>{facility.name}</Text>
                <Text style={styles.tableCell}>{facility.devices}</Text>
                <Text style={styles.tableCell}>{facility.alerts}</Text>
                <Text style={styles.tableCell}>{facility.patients}</Text>
              </View>
            ))}
          </View>
        </View>

        <Card title="Alerts">
          {alertsData.map((alert, index) => (
            <View key={index} style={styles.alertItem}>
              <View style={[styles.alertIndicator, { backgroundColor: alert.color }]} />
              <View style={styles.alertInfo}>
                <Text style={styles.alertType}>{alert.type}</Text>
                <Text style={styles.alertCount}>{alert.count}</Text>
              </View>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${alert.progress}%`, backgroundColor: alert.color },
                  ]}
                />
              </View>
            </View>
          ))}
        </Card>

        <Card title="Alerts per facility">
          <AlertItem title="Boca Circle" subtitle="Facility" value="100" />
          <AlertItem title="Boca Circle" subtitle="Facility" value="100" />
        </Card>

        {/* Patients and Compliance Chart */}
        <PatientsCompliance totalPatients={2579} totalReadings={2579} changePercentage="+2.45%" />
      </ScrollView>
    </SafeAreaView>
  );
};

const facilitiesData = [
  { name: 'Boca Circle', devices: 200, alerts: 8, patients: 233 },
  { name: 'Jackson', devices: 100, alerts: 12, patients: 250 },
  { name: 'Ashure', devices: 103, alerts: 6, patients: 152 },
  { name: 'Victoria Cross', devices: 100, alerts: 2, patients: 120 },
  { name: 'Beach Breeze', devices: 50, alerts: 4, patients: 100 },
];

const alertsData = [
  { type: 'Normal', count: '1,232', progress: 85, color: '#10B981' },
  { type: 'Critical', count: '200', progress: 25, color: '#EF4444' },
  { type: 'Warning', count: '150', progress: 45, color: '#F59E0B' },
  { type: 'Pending Reading', count: '840', progress: 65, color: '#6B7280' },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  greeting: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  section: {
    backgroundColor: '#FFFFFF80',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    // elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  table: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f9fafb',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  tableHeaderText: {
    flex: 1,
    fontSize: 12,
    fontWeight: '600',
    color: '#6b7280',
    textTransform: 'uppercase',
  },
  tableRow: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  tableCell: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
  },
  alertsContainer: {
    gap: 12,
    backgroundColor: 'white',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  alertItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  alertIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  alertInfo: {
    flex: 1,
  },
  alertType: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  alertCount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  progressBar: {
    width: 100,
    height: 4,
    backgroundColor: '#e5e7eb',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
});

export default DashboardView;
