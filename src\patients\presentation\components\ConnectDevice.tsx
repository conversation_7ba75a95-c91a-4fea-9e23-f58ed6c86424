import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface Props {
  onConnectDevice: () => void;
}

export const ConnectDevice = ({ onConnectDevice }: Props) => {
  return (
    <View style={styles.content}>
      <Text style={styles.title}>No Device Connected</Text>
      <Text style={styles.description}>Please connect a device to continue</Text>
      <TouchableOpacity style={styles.connectButton} onPress={onConnectDevice}>
        <Text style={styles.connectButtonText}>Connect Device</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    backgroundColor: 'transparent',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '500',
    fontFamily: 'DMSans',
    color: '#2B3674',
  },
  description: {
    fontSize: 14,
    fontFamily: 'DMSans',
    fontWeight: '400',
  },
  connectButton: {
    backgroundColor: '#3366FF',
    padding: 12,
    borderRadius: 999,
  },
  connectButtonText: {
    fontSize: 14,
    fontFamily: 'DMSans',
    fontWeight: '600',
    color: '#fff',
  },
});
