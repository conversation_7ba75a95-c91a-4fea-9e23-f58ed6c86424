// Smart Ring Commands (J Style)
export const SMART_RING_COMMANDS: Record<string, number> = {
  REAL_TIME_STEP_COUNTING: 0x09,
} as const;

export const OXIMETER_COMMANDS: Record<string, number> = {
  PLETHYSMOGRAM_DATA: 0x80,
  SPO2_AND_PR_DATA: 0x81,
  ALARM_LIMIT_DATA: 0x82,
};

// Service UUIDs for device identification
export const DEVICE_SERVICE_UUIDS = {
  ring: '0000fff0-0000-1000-8000-00805f9b34fb',
  oximeter: 'CDEACB80-5235-4C07-8846-93A37EE6B86D',
  // bracelet: 'future-bracelet-uuid'
} as const;

export const DEVICE_CHARACTERISTICS = {
  ring: {
    serviceUUID: '0000fff0-0000-1000-8000-00805f9b34fb',
    txUUID: '0000fff6-0000-1000-8000-00805f9b34fb', // Write
    rxUUID: '0000fff7-0000-1000-8000-00805f9b34fb', // Notify
  },
  oximeter: {
    serviceUUID: 'CDEACB80-5235-4C07-8846-93A37EE6B86D',
    txUUID: 'CDEACB82-5235-4C07-8846-93A37EE6B86D', // Write
    rxUUID: 'CDEACB81-5235-4C07-8846-93A37EE6B86D', // Read/Notify
  },
} as const;
