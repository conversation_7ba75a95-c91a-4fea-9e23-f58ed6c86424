import { Container } from 'inversify';
import { coreContainer } from '../../core/dependency-injection/inversify.config';
import { GetPatients } from '../capabilities/get-patients';
import { PATIENT_SYMBOLS } from '../business/types/patient-symbols';
import { PatientService } from './api/patient.service';

const patientContainer = new Container({ parent: coreContainer });

patientContainer.bind(PATIENT_SYMBOLS.PatientService).to(PatientService);
patientContainer.bind(PATIENT_SYMBOLS.GetPatients).to(GetPatients);

export { patientContainer };
