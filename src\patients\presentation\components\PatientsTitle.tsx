import { HeaderTitleProps } from '@react-navigation/elements';
import { useLocalSearchParams } from 'expo-router';

import { StyleSheet, Text, View } from 'react-native';

export const PatientsTitle = (props: HeaderTitleProps) => {
  const { facilityId, facilityName } = useLocalSearchParams<{
    facilityId?: string;
    facilityName?: string;
  }>();

  console.log(facilityId, facilityName);

  return (
    <View style={styles.container}>
      {/* <Text style={styles.title}>{facilityName}</Text> */}
      <Text style={styles.title}>Patients</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    fontFamily: 'Roboto',
  },
});
