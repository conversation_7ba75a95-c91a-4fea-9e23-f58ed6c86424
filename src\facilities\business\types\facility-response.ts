export interface FacilityResponse {
  data: Facility[];
  success: boolean;
}

export interface Facility {
  id: string;
  name: string;
  type: Type[];
  extension: Extension[];
  resourceType: string;
}

export interface Extension {
  url: string;
  valueString?: string;
  valueInstant?: Date;
  valueInteger?: number;
}

export interface Type {
  coding: Coding[];
}

export interface Coding {
  code: string;
  system: string;
  display: string;
}
