import React, { Suspense, lazy } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

// Lazy load the SinglePatientView component
const SinglePatientViewComponent = lazy(() => import('./SinglePatientView'));
// Fallback component while loading
const SinglePatientViewFallback = () => (
  <View style={styles.fallbackContainer}>
    <View style={styles.loadingIndicator}>
      <ActivityIndicator size="large" color="#4A90E2" />
    </View>
  </View>
);

// Main lazy wrapper component
export const LazySinglePatientView = () => {
  return (
    <Suspense fallback={<SinglePatientViewFallback />}>
      <SinglePatientViewComponent />
    </Suspense>
  );
};

const styles = StyleSheet.create({
  fallbackContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    padding: 16,
  },
  loadingIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
  },
});

export default LazySinglePatientView;
