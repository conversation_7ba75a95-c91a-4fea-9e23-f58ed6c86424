import { Ionicons } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import React, { memo, useCallback, useMemo } from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { COLORS } from '../../business/constants/colors';

export interface DataTableColumn<T> {
  key: keyof T | string;
  header: string;
  width: number;
  align?: 'left' | 'center' | 'right';
  render?: (item: T, value: any) => React.ReactNode;
}

interface DataTableProps<T> {
  data: T[];
  columns: DataTableColumn<T>[];
  onRowPress?: (item: T) => void;
  keyExtractor: (item: T, index: number) => string;
  showChevron?: boolean;
  headerStyle?: object;
  rowStyle?: object;
  containerStyle?: object;

  // Infinite scroll support
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  isLoadingMore?: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
}

const ESTIMATED_ROW_HEIGHT = 40;

function DataTable<T>({
  data,
  columns,
  onRowPress,
  keyExtractor,
  showChevron = false,
  headerStyle = {},
  rowStyle = {},
  containerStyle = {},
  onEndReached,
  onEndReachedThreshold = 0.1,
  isLoadingMore = false,
  onRefresh,
  refreshing = false,
}: DataTableProps<T>) {
  // Calculate column widths as percentages for responsive design
  const totalColumnsWidth = columns.reduce((sum, col) => sum + col.width, 0);
  const columnsWithPercentages = columns.map((column) => ({
    ...column,
    widthPercentage: (column.width / totalColumnsWidth) * (showChevron ? 90 : 100),
  }));

  // Render table header
  const renderHeader = useMemo(
    () => (
      <View style={[styles.headerContainer, headerStyle]}>
        {columnsWithPercentages.map((column) => (
          <View
            key={String(column.key)}
            style={[
              styles.headerCell,
              { width: `${column.widthPercentage}%` },
              column.align === 'left' && styles.alignLeft,
              column.align === 'center' && styles.alignCenter,
              column.align === 'right' && styles.alignRight,
            ]}
          >
            <Text style={styles.headerText}>{column.header}</Text>
          </View>
        ))}
        {showChevron && <View style={[styles.chevronHeaderCell, { width: `10%` }]} />}
      </View>
    ),
    [columnsWithPercentages, showChevron, headerStyle]
  );

  // Render individual row
  const renderRow = useCallback(
    ({ item }: { item: T }) => {
      return (
        <TouchableOpacity
          style={[styles.rowContainer, rowStyle]}
          onPress={() => onRowPress?.(item)}
          activeOpacity={onRowPress ? 0.7 : 1}
          disabled={!onRowPress}
        >
          {columnsWithPercentages.map((column) => {
            const value = column.key === 'custom' ? null : (item as any)[column.key];
            const content = column.render
              ? column.render(item, value)
              : value != null
                ? String(value)
                : '';

            return (
              <View
                key={String(column.key)}
                style={[
                  styles.cell,
                  { width: `${column.widthPercentage}%` },
                  column.align === 'left' && styles.alignLeft,
                  column.align === 'center' && styles.alignCenter,
                  column.align === 'right' && styles.alignRight,
                ]}
              >
                {typeof content === 'string' ? (
                  <Text style={styles.cellText} numberOfLines={1}>
                    {content}
                  </Text>
                ) : (
                  content
                )}
              </View>
            );
          })}
          {showChevron && (
            <View style={[styles.chevronCell, { width: `10%` }]}>
              <Ionicons name="chevron-forward" size={10} color={COLORS.ACCENT} />
            </View>
          )}
        </TouchableOpacity>
      );
    },
    [columnsWithPercentages, showChevron, onRowPress, rowStyle]
  );

  // Render footer loading indicator
  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={COLORS.ACCENT} />
      </View>
    );
  }, [isLoadingMore]);

  // Safe key extractor to prevent undefined toString errors
  const safeKeyExtractor = useCallback(
    (item: T, index: number) => {
      try {
        const key = keyExtractor(item, index);
        return key != null ? String(key) : `item-${index}`;
      } catch {
        return `item-${index}`;
      }
    },
    [keyExtractor]
  );

  return (
    <View style={[styles.container, containerStyle]}>
      {renderHeader}
      <FlashList
        data={data}
        renderItem={renderRow}
        keyExtractor={safeKeyExtractor}
        estimatedItemSize={ESTIMATED_ROW_HEIGHT}
        onEndReached={onEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        ListFooterComponent={renderFooter}
        refreshing={refreshing}
        onRefresh={onRefresh}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.LIGHT_WHITE,
    flex: 1,
  },
  listContainer: {
    paddingBottom: 10,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 10,
    backgroundColor: COLORS.LIGHT_GRAY,
    minHeight: ESTIMATED_ROW_HEIGHT,
  },
  headerCell: {
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  headerText: {
    fontSize: 7,
    fontFamily: 'DMSans-Regular',
    fontWeight: '700',
    color: COLORS.TEXT_TITLE,
    textTransform: 'uppercase',
    letterSpacing: 0.2,
    textAlign: 'center',
  },
  chevronHeaderCell: {
    alignItems: 'center',
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 10,
    minHeight: ESTIMATED_ROW_HEIGHT,
  },
  cell: {
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  cellText: {
    fontSize: 7,
    fontFamily: 'DMSans-Regular',
    fontWeight: '500',
    color: COLORS.TEXT_MAIN,
    textAlign: 'center',
  },
  chevronCell: {
    alignItems: 'center',
  },
  alignLeft: {
    alignItems: 'flex-start',
  },
  alignCenter: {
    alignItems: 'center',
  },
  alignRight: {
    alignItems: 'flex-end',
  },
  footerLoader: {
    paddingVertical: 10,
    alignItems: 'center',
  },
});

export default memo(DataTable) as <T>(props: DataTableProps<T>) => React.ReactElement;
