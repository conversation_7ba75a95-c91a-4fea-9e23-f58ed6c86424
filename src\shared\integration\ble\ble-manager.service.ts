import * as ExpoDevice from 'expo-device';
import { inject, injectable } from 'inversify';
import { PermissionsAndroid, Platform } from 'react-native';
import base64 from 'react-native-base64';
import { Ble<PERSON>rror, BleManager, Characteristic, Device } from 'react-native-ble-plx';

import {
  createHeartRateCommand,
  createLiveMeasurementCommand,
  DEVICE_FILTERS,
  HEART_RATE_ACTIONS,
  SMART_RING_COMMANDS,
  SMART_RING_RX_UUID,
  SMART_RING_SERVICE_UUID,
  SMART_RING_TX_UUID,
} from '../../business/constants/ble-constants';
import { ILogger } from '../../business/interfaces';
import { IBleManager } from '../../business/interfaces/ble-manager.interface';
import {
  BleConnectionOptions,
  BlePermissionStatus,
  BleScanOptions,
  BleState,
  HeartRateData,
  HeartRateRequestParams,
  LiveMeasurementParams,
  LiveMeasurementResponse,
  LiveMeasurementStopResponse,
  SmartRingActivityData,
  SmartRingContinuousHeartRateData,
  SmartRingHeartRateResponse,
  SmartRingOxygenData,
  SmartRingSingleHeartRateData,
} from '../../business/types/ble-types';
import { SHARED_DEPENDENCY_INJECTION_TYPES } from '../../business/types/shared-symbols';

const HEART_RATE_CMD = '0A'; // Command to start heart rate monitoring
const TEST_CMD = '9801';

@injectable()
export class BleManagerService implements IBleManager {
  private bleManager: BleManager;
  private heartRateCallbacks: Map<
    string,
    (error: BleError | null, data: HeartRateData | null) => void
  > = new Map();

  // Singleton connection management
  private connectedDevice: Device | null = null;
  private connectedDeviceId: string | null = null;
  private isConnecting: boolean = false;

  constructor(
    @inject(SHARED_DEPENDENCY_INJECTION_TYPES.Logger)
    private readonly logger: ILogger
  ) {
    this.bleManager = new BleManager();
  }

  async getState(): Promise<BleState> {
    return this.bleManager.state();
  }

  // Connection management helpers
  private async validateConnection(): Promise<Device> {
    if (!this.connectedDevice || !this.connectedDeviceId) {
      throw new Error('No device connected. Please connect to a device first.');
    }

    // Verify the connection is still active
    try {
      const isStillConnected = await this.connectedDevice.isConnected();
      if (!isStillConnected) {
        this.logger.warn('Device connection lost, clearing stored connection', 'ble-manager');
        this.clearConnectionState();
        throw new Error('Device connection lost. Please reconnect.');
      }
      return this.connectedDevice;
    } catch (error) {
      this.logger.error('Error validating connection:', 'ble-manager', { error });
      this.clearConnectionState();
      throw new Error('Device connection validation failed. Please reconnect.');
    }
  }

  private clearConnectionState(): void {
    this.connectedDevice = null;
    this.connectedDeviceId = null;
    this.isConnecting = false;
    this.heartRateCallbacks.clear();
    this.logger.debug('Connection state cleared', 'ble-manager');
  }

  private async ensureServicesDiscovered(): Promise<void> {
    if (!this.connectedDevice) {
      throw new Error('No connected device');
    }

    try {
      // Only discover if not already discovered
      await this.connectedDevice.discoverAllServicesAndCharacteristics();
      this.logger.debug('Services and characteristics ensured', 'ble-manager');
    } catch (error) {
      this.logger.error('Error ensuring services discovered:', 'ble-manager', { error });
      throw error;
    }
  }

  onStateChange(callback: (state: BleState) => void): () => void {
    const subscription = this.bleManager.onStateChange(callback, true);
    return () => subscription.remove();
  }

  async requestPermissions(): Promise<BlePermissionStatus> {
    if (Platform.OS === 'android') {
      if ((ExpoDevice.platformApiLevel ?? -1) < 31) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'Bluetooth Low Energy requires Location permission to scan for devices',
            buttonPositive: 'OK',
          }
        );
        return {
          granted: granted === PermissionsAndroid.RESULTS.GRANTED,
          message:
            granted !== PermissionsAndroid.RESULTS.GRANTED
              ? 'Location permission denied'
              : undefined,
        };
      } else {
        return this.requestAndroid31Permissions();
      }
    } else {
      return { granted: true };
    }
  }

  private async requestAndroid31Permissions(): Promise<BlePermissionStatus> {
    const bluetoothScanPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
      {
        title: 'Bluetooth Scan Permission',
        message: 'This app needs Bluetooth scan permission to discover devices',
        buttonPositive: 'OK',
      }
    );
    const bluetoothConnectPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
      {
        title: 'Bluetooth Connect Permission',
        message: 'This app needs Bluetooth connect permission to connect to devices',
        buttonPositive: 'OK',
      }
    );
    const fineLocationPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: 'Location Permission',
        message: 'Bluetooth Low Energy requires Location permission to scan for devices',
        buttonPositive: 'OK',
      }
    );

    const allGranted =
      bluetoothScanPermission === 'granted' &&
      bluetoothConnectPermission === 'granted' &&
      fineLocationPermission === 'granted';

    return {
      granted: allGranted,
      message: !allGranted ? 'One or more Bluetooth permissions were denied' : undefined,
    };
  }

  startDeviceScan(
    serviceUUIDs?: string[] | null,
    options?: BleScanOptions,
    callback?: (error: BleError | null, device: Device | null) => void
  ): void {
    const scanOptions = options
      ? {
          allowDuplicates: options.allowDuplicates ?? false,
          scanMode: options.scanMode,
        }
      : null;

    // Enhanced device filtering callback wrapper
    const deviceFilterCallback = (error: BleError | null, device: Device | null) => {
      if (error || !device) {
        callback?.(error, device);
        return;
      }

      // Apply device name filters
      const deviceName = device.name || '';
      const isExcluded = DEVICE_FILTERS.excludeNames.some((name: string) =>
        deviceName.toLowerCase().includes(name.toLowerCase())
      );

      // Show all devices with names, only exclude specific ones
      if (deviceName && !isExcluded) {
        this.logger.info(`Found BLE device: ${deviceName} (${device.id})`, 'ble-manager');
        callback?.(error, device);
      } else {
        this.logger.debug(`Filtered out device: ${deviceName}`, 'ble-manager');
      }
    };

    // Scan for J Style smart rings specifically
    if (options?.heartRateFirst) {
      // First scan for J Style smart ring service
      this.bleManager.startDeviceScan([SMART_RING_SERVICE_UUID], scanOptions, deviceFilterCallback);

      // Fallback to general scan after timeout
      if (options.timeout) {
        setTimeout(() => {
          this.bleManager.stopDeviceScan();
          this.bleManager.startDeviceScan(null, scanOptions, deviceFilterCallback);
        }, options.timeout / 2);
      }
    } else {
      this.bleManager.startDeviceScan(serviceUUIDs || null, scanOptions, deviceFilterCallback);
    }

    // Auto-stop after timeout
    if (options?.timeout) {
      setTimeout(() => {
        this.stopDeviceScan();
      }, options.timeout);
    }
  }

  stopDeviceScan(): void {
    this.bleManager.stopDeviceScan();
  }

  async connectToDevice(deviceId: string, options?: BleConnectionOptions): Promise<Device> {
    // If already connecting to this device, wait for completion
    if (this.isConnecting && this.connectedDeviceId === deviceId) {
      this.logger.info('Connection already in progress, waiting...', 'ble-manager');
      while (this.isConnecting) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
      if (this.connectedDevice) {
        return this.connectedDevice;
      }
    }

    // If already connected to this device, return existing connection
    if (this.connectedDevice && this.connectedDeviceId === deviceId) {
      try {
        const isConnected = await this.connectedDevice.isConnected();
        if (isConnected) {
          this.logger.info('Reusing existing connection', 'ble-manager', { deviceId });
          return this.connectedDevice;
        } else {
          this.logger.warn('Stored connection is stale, creating new one', 'ble-manager');
          this.clearConnectionState();
        }
      } catch (connectionError) {
        this.logger.warn('Error checking existing connection, creating new one', 'ble-manager', {
          connectionError,
        });
        this.clearConnectionState();
      }
    }

    // If connected to a different device, disconnect first
    if (this.connectedDevice && this.connectedDeviceId !== deviceId) {
      this.logger.info(
        'Disconnecting from previous device before connecting to new one',
        'ble-manager'
      );
      await this.disconnectFromDevice(this.connectedDeviceId!);
    }

    // Create new connection
    this.isConnecting = true;
    this.connectedDeviceId = deviceId;

    try {
      const connectionOptions = {
        requestMTU: options?.requestMTU ?? 512,
        timeout: options?.timeout ?? 10000,
      };

      this.logger.info(`Connecting to J Style smart ring: ${deviceId}`, 'ble-manager');
      const device = await this.bleManager.connectToDevice(deviceId, connectionOptions);

      // Verify connection
      const isConnected = await device.isConnected();
      if (!isConnected) {
        throw new Error('Device connection failed - not connected after connection attempt');
      }

      // Store the connection
      this.connectedDevice = device;
      this.isConnecting = false;

      // Discover services and characteristics
      await this.ensureServicesDiscovered();

      this.logger.info(`J Style smart ring connected successfully: ${device.id}`, 'ble-manager');
      return device;
    } catch (error) {
      this.logger.error(`Error connecting to device: ${error}`, 'ble-manager');
      this.clearConnectionState();
      throw error;
    }
  }

  async disconnectFromDevice(deviceId: string): Promise<void> {
    try {
      // Validate we're disconnecting from the correct device
      if (this.connectedDeviceId && this.connectedDeviceId !== deviceId) {
        this.logger.warn(
          `Disconnect requested for ${deviceId} but connected to ${this.connectedDeviceId}`,
          'ble-manager'
        );
      }

      if (this.connectedDevice) {
        this.logger.info(`Disconnecting from J Style smart ring: ${deviceId}`, 'ble-manager');

        // Stop any ongoing heart rate monitoring
        if (this.heartRateCallbacks.has(deviceId)) {
          this.logger.debug('Stopping heart rate monitoring before disconnect', 'ble-manager');
          await this.stopHeartRateMonitoring(deviceId);
        }

        // Cancel the device connection
        await this.bleManager.cancelDeviceConnection(deviceId);

        this.logger.info(`Disconnected from J Style smart ring: ${deviceId}`, 'ble-manager');
      } else {
        this.logger.info(`No active connection to disconnect from ${deviceId}`, 'ble-manager');
      }

      // Clear all connection state
      this.clearConnectionState();
    } catch (error) {
      this.logger.error(`Error disconnecting from device: ${error}`, 'ble-manager');
      // Clear state even if disconnection failed
      this.clearConnectionState();
      throw error;
    }
  }

  async isDeviceConnected(deviceId: string): Promise<boolean> {
    try {
      // Check if we have the device connected in our singleton state
      if (this.connectedDevice && this.connectedDeviceId === deviceId) {
        return await this.connectedDevice.isConnected();
      }
      return false;
    } catch {
      return false;
    }
  }

  onDeviceDisconnected(
    deviceId: string,
    callback: (error: BleError | null, device: Device | null) => void
  ): () => void {
    const subscription = this.bleManager.onDeviceDisconnected(deviceId, (error, device) => {
      this.heartRateCallbacks.delete(deviceId);
      callback(error, device);
    });
    return () => subscription.remove();
  }

  async discoverAllServicesAndCharacteristics(deviceId: string): Promise<Device> {
    const device = await this.bleManager.connectToDevice(deviceId);
    this.logger.info(
      'Discovering J Style smart ring services and characteristics...',
      'ble-manager'
    );
    await device.discoverAllServicesAndCharacteristics();
    this.logger.info('J Style smart ring services and characteristics discovered', 'ble-manager');
    return device;
  }

  async getServices(deviceId: string): Promise<any[]> {
    const device = await this.bleManager.connectToDevice(deviceId);
    return await device.services();
  }

  async startHeartRateMonitoring(
    deviceId: string,
    callback: (error: BleError | null, data: HeartRateData | null) => void
  ): Promise<void> {
    try {
      this.logger.info(
        `Starting heart rate monitoring on J Style smart ring: ${deviceId}`,
        'ble-manager'
      );

      // Use singleton connection instead of reconnecting
      const device = await this.validateConnection();
      await this.ensureServicesDiscovered();
      const services = await device.services();
      this.logger.debug('Services:', 'ble-manager', { services });

      // Find the J Style smart ring service
      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        this.logger.warn('J Style smart ring service not found on device', 'ble-manager');
        this.logger.debug('Available services:', 'ble-manager', {
          availableServices: services.map((s) => s.uuid),
        });
        throw new Error('J Style smart ring service not available on this device');
      }

      // Store callback for this device
      this.heartRateCallbacks.set(deviceId, callback);

      // Start monitoring heart rate data from RX characteristic
      device.monitorCharacteristicForService(
        smartRingService.uuid,
        SMART_RING_RX_UUID,
        (error, characteristic) => this.onHeartRateUpdate(deviceId, error, characteristic)
      );

      // Send command to start heart rate monitoring
      const heartRateCommand = base64.encode(HEART_RATE_CMD);
      await device.writeCharacteristicWithResponseForService(
        smartRingService.uuid,
        SMART_RING_TX_UUID,
        heartRateCommand
      );

      this.logger.info(
        'Heart rate monitoring started successfully on J Style smart ring',
        'ble-manager'
      );
    } catch (error) {
      this.logger.error(`Error starting heart rate monitoring: ${error}`, 'ble-manager');
      throw error;
    }
  }

  async stopHeartRateMonitoring(deviceId: string): Promise<void> {
    try {
      this.heartRateCallbacks.delete(deviceId);
      this.logger.info(
        `Heart rate monitoring stopped for J Style smart ring: ${deviceId}`,
        'ble-manager'
      );
    } catch (error) {
      this.logger.error(`Error stopping heart rate monitoring: ${error}`, 'ble-manager');
      throw error;
    }
  }

  async resetEnergyExpended(deviceId: string): Promise<void> {
    try {
      this.logger.warn(
        `Reset energy expended not supported by J Style smart ring ${deviceId}`,
        'ble-manager'
      );
      throw new Error('Energy expended reset not supported by J Style smart ring');
    } catch (error) {
      this.logger.error(`Error resetting energy expended: ${error}`, 'ble-manager');
      throw error;
    }
  }

  async readSensorLocation(deviceId: string): Promise<string | null> {
    try {
      this.logger.info(`J Style smart ring ${deviceId} sensor location: Finger`, 'ble-manager');
      return 'Finger';
    } catch (error) {
      this.logger.error(`Error reading sensor location: ${error}`, 'ble-manager');
      throw error;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async testConnection(_deviceId: string): Promise<string | null> {
    try {
      this.logger.info('Testing J Style smart ring connection...', 'ble-manager');

      // Use singleton connection instead of reconnecting
      const device = await this.validateConnection();
      await this.ensureServicesDiscovered();

      // Get all services
      const services = await device.services();
      this.logger.debug('Available services:', 'ble-manager', {
        availableServices: services.map((s) => s.uuid),
      });

      // Find the J Style smart ring service
      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (smartRingService) {
        this.logger.info(
          `Found J Style smart ring service: ${smartRingService.uuid}`,
          'ble-manager'
        );

        // Send test command to TX characteristic
        const testCommand = base64.encode(TEST_CMD);
        await device.writeCharacteristicWithResponseForService(
          smartRingService.uuid,
          SMART_RING_TX_UUID,
          testCommand
        );

        this.logger.info('Test connection successful! J Style smart ring responded', 'ble-manager');
        return 'J Style Smart Ring Connected';
      } else {
        this.logger.warn('J Style smart ring service not found', 'ble-manager');
        return null;
      }
    } catch (error) {
      this.logger.error(`Error testing connection: ${error}`, 'ble-manager');
      throw error;
    }
  }

  monitorCharacteristic(
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string,
    callback: (error: BleError | null, characteristic: Characteristic | null) => void
  ): void {
    this.bleManager
      .connectToDevice(deviceId)
      .then((device) => {
        device.monitorCharacteristicForService(serviceUUID, characteristicUUID, callback);
      })
      .catch((error) => {
        callback(error, null);
      });
  }

  async readCharacteristic(
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string
  ): Promise<Characteristic> {
    const device = await this.bleManager.connectToDevice(deviceId);
    return await device.readCharacteristicForService(serviceUUID, characteristicUUID);
  }

  async writeCharacteristic(
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string,
    value: string
  ): Promise<Characteristic> {
    const device = await this.bleManager.connectToDevice(deviceId);
    return await device.writeCharacteristicWithResponseForService(
      serviceUUID,
      characteristicUUID,
      value
    );
  }

  private onHeartRateUpdate = (
    deviceId: string,
    error: BleError | null,
    characteristic: Characteristic | null
  ) => {
    const callback = this.heartRateCallbacks.get(deviceId);
    if (!callback) return;

    if (error) {
      this.logger.error(`Heart rate update error: ${error}`, 'ble-manager');
      callback(error, null);
      return;
    }

    if (!characteristic?.value) {
      this.logger.warn('No heart rate data received from J Style smart ring', 'ble-manager');
      callback(null, null);
      return;
    }

    // Filter out non-heart-rate data to prevent parsing errors
    try {
      const data = base64.decode(characteristic.value);
      const bytes = new Uint8Array(data.split('').map((char: string) => char.charCodeAt(0)));

      if (bytes.length > 0) {
        const commandType = bytes[0];

        // Only process heart rate commands (0x54, 0x55) in the permanent monitoring
        // Ignore other commands like battery (0x13), oxygen (0x66), etc.
        if (
          commandType !== SMART_RING_COMMANDS.CMD_Get_HeartData &&
          commandType !== SMART_RING_COMMANDS.GET_SINGLE_HEART_RATE_DATA
        ) {
          this.logger.debug(
            `Ignoring non-heart-rate command 0x${commandType.toString(16)} in heart rate monitoring`,
            'ble-manager'
          );
          return; // Don't try to parse non-heart-rate data
        }
      }
    } catch {
      this.logger.debug('Error filtering heart rate data, skipping...', 'ble-manager');
      return; // If we can't even decode it, it's probably not for us
    }

    const heartRateData = this.parseJStyleHeartRateData(characteristic.value);
    if (heartRateData) {
      this.logger.info('J Style Smart Ring Heart Rate Data:', 'ble-manager', { heartRateData });
      callback(null, heartRateData);
    } else {
      // Don't error on failed parsing - just log and ignore
      this.logger.debug('Could not parse as heart rate data, ignoring...', 'ble-manager');
    }
  };

  async requestHeartRateData(
    deviceId: string,
    params: HeartRateRequestParams
  ): Promise<SmartRingHeartRateResponse> {
    try {
      this.logger.info('Requesting heart rate data from J Style smart ring:', 'ble-manager', {
        deviceId,
        params,
      });

      const device = await this.bleManager.connectToDevice(deviceId);
      await device.discoverAllServicesAndCharacteristics();
      const services = await device.services();

      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        throw new Error('J Style smart ring service not available on this device');
      }

      // Create command based on action type
      let actionCode: number;
      switch (params.action) {
        case 'delete_all':
          actionCode = HEART_RATE_ACTIONS.DELETE_ALL;
          break;
        case 'read_latest':
          actionCode = HEART_RATE_ACTIONS.READ_LATEST;
          break;
        case 'read_at_location':
          actionCode = HEART_RATE_ACTIONS.READ_AT_LOCATION;
          break;
        case 'continue_from_last':
          actionCode = HEART_RATE_ACTIONS.CONTINUE_FROM_LAST;
          break;
        default:
          actionCode = HEART_RATE_ACTIONS.READ_LATEST;
      }

      // Create the command
      const command = createHeartRateCommand(SMART_RING_COMMANDS.CMD_Get_HeartData, actionCode);

      // Send command
      const commandBase64 = base64.encode(String.fromCharCode(...command));
      await device.writeCharacteristicWithResponseForService(
        smartRingService.uuid,
        SMART_RING_TX_UUID,
        commandBase64
      );

      // Read response
      const response = await device.readCharacteristicForService(
        smartRingService.uuid,
        SMART_RING_RX_UUID
      );

      if (!response.value) {
        throw new Error('No response received from smart ring');
      }

      return this.parseSmartRingHeartRateResponse(response.value);
    } catch (error) {
      this.logger.error(`Error requesting heart rate data: ${error}`, 'ble-manager');
      throw error;
    }
  }

  async requestSingleHeartRateData(
    deviceId: string,
    params: HeartRateRequestParams
  ): Promise<SmartRingHeartRateResponse> {
    try {
      this.logger.info(
        'Requesting single heart rate data from J Style smart ring:',
        'ble-manager',
        {
          deviceId,
          params,
        }
      );

      const device = await this.bleManager.connectToDevice(deviceId);
      this.logger.debug('✅ Device connected successfully', 'ble-manager');

      await device.discoverAllServicesAndCharacteristics();
      this.logger.debug('✅ Services and characteristics discovered', 'ble-manager');

      const services = await device.services();
      this.logger.debug('📋 Available services:', 'ble-manager', {
        services: services.map((s) => ({ uuid: s.uuid, isPrimary: s.isPrimary })),
      });

      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        this.logger.error('❌ J Style smart ring service not found', 'ble-manager', {
          expectedServiceUUID: SMART_RING_SERVICE_UUID,
          availableServices: services.map((s) => s.uuid),
        });
        throw new Error('J Style smart ring service not available on this device');
      }

      this.logger.info('✅ Found smart ring service:', 'ble-manager', {
        serviceUUID: smartRingService.uuid,
      });

      // Get characteristics for debugging
      const characteristics = await smartRingService.characteristics();
      this.logger.debug('📋 Available characteristics:', 'ble-manager', {
        characteristics: characteristics.map((c) => ({
          uuid: c.uuid,
          isReadable: c.isReadable,
          isWritableWithResponse: c.isWritableWithResponse,
          isWritableWithoutResponse: c.isWritableWithoutResponse,
          isNotifiable: c.isNotifiable,
          isIndicatable: c.isIndicatable,
        })),
      });

      // Create command based on action type
      let actionCode: number;
      switch (params.action) {
        case 'delete_all':
          actionCode = HEART_RATE_ACTIONS.DELETE_ALL;
          break;
        case 'read_latest':
          actionCode = HEART_RATE_ACTIONS.READ_LATEST;
          break;
        case 'read_at_location':
          actionCode = HEART_RATE_ACTIONS.READ_AT_LOCATION;
          break;
        case 'continue_from_last':
          actionCode = HEART_RATE_ACTIONS.CONTINUE_FROM_LAST;
          break;
        default:
          actionCode = HEART_RATE_ACTIONS.READ_LATEST;
      }

      // Create the command
      const command = createHeartRateCommand(
        SMART_RING_COMMANDS.GET_SINGLE_HEART_RATE_DATA,
        actionCode
      );

      this.logger.debug('📤 Sending command:', 'ble-manager', {
        command: Array.from(command)
          .map((b) => '0x' + b.toString(16).padStart(2, '0'))
          .join(' '),
        commandLength: command.length,
        txCharacteristicUUID: SMART_RING_TX_UUID,
      });

      // Send command
      const commandBase64 = base64.encode(String.fromCharCode(...command));

      try {
        await device.writeCharacteristicWithResponseForService(
          smartRingService.uuid,
          SMART_RING_TX_UUID,
          commandBase64
        );
        this.logger.debug('✅ Command sent successfully to TX characteristic', 'ble-manager');
      } catch (writeError) {
        this.logger.error('❌ Failed to write to TX characteristic:', 'ble-manager', {
          error: writeError,
          txUUID: SMART_RING_TX_UUID,
        });
        throw writeError;
      }

      // Read response
      this.logger.debug('📥 Attempting to read from RX characteristic:', 'ble-manager', {
        rxCharacteristicUUID: SMART_RING_RX_UUID,
      });

      try {
        const response = await device.readCharacteristicForService(
          smartRingService.uuid,
          SMART_RING_RX_UUID
        );

        this.logger.debug('✅ Response received from RX characteristic', 'ble-manager', {
          hasValue: !!response.value,
          valueLength: response.value?.length,
        });

        if (!response.value) {
          throw new Error('No response received from smart ring');
        }

        return this.parseSmartRingHeartRateResponse(response.value);
      } catch (readError) {
        this.logger.error('❌ Failed to read from RX characteristic:', 'ble-manager', {
          error: readError,
          rxUUID: SMART_RING_RX_UUID,
        });
        throw readError;
      }
    } catch (error) {
      this.logger.error(`Error requesting single heart rate data: ${error}`, 'ble-manager');
      throw error;
    }
  }

  async deleteAllHeartRateData(deviceId: string): Promise<void> {
    try {
      await this.requestHeartRateData(deviceId, { action: 'delete_all' });
      this.logger.info('All heart rate data deleted from smart ring', 'ble-manager');
    } catch (error) {
      this.logger.error(`Error deleting heart rate data: ${error}`, 'ble-manager');
      throw error;
    }
  }

  async getBatteryLevel(deviceId: string): Promise<number> {
    try {
      this.logger.info('Requesting battery level from J Style smart ring', 'ble-manager');

      // Use singleton connection instead of reconnecting
      const device = await this.validateConnection();
      await this.ensureServicesDiscovered();
      const services = await device.services();

      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        throw new Error('J Style smart ring service not available on this device');
      }

      // Create simple battery command (matching official SDK GetDeviceBatteryLevel exactly)
      const command = new Uint8Array(16);
      command[0] = SMART_RING_COMMANDS.CMD_Get_BatteryLevel; // 0x13

      // Calculate CRC exactly like official SDK crcValue() method
      let crc = 0;
      for (let i = 0; i < 15; i++) {
        crc += command[i];
      }
      command[15] = crc & 0xff;

      this.logger.debug('📤 Sending battery level command:', 'ble-manager', {
        command: Array.from(command)
          .map((b) => '0x' + b.toString(16).padStart(2, '0'))
          .join(' '),
        commandLength: command.length,
        crcCalculated: `0x${(crc & 0xff).toString(16).padStart(2, '0')}`,
      });

      const commandBase64 = base64.encode(String.fromCharCode(...command));

      await device.writeCharacteristicWithResponseForService(
        smartRingService.uuid,
        SMART_RING_TX_UUID,
        commandBase64
      );

      // Use notification-based approach (following Android implementation pattern)
      this.logger.debug('🔄 Switching to notification-based battery request', 'ble-manager');
      return await this.getBatteryLevelWithNotification(deviceId);
    } catch (error) {
      this.logger.error(`Error getting battery level: ${error}`, 'ble-manager');
      throw error;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async getBatteryLevelWithNotification(deviceId: string): Promise<number> {
    return new Promise(async (resolve, reject) => {
      try {
        this.logger.info(
          'Requesting battery level using notifications (Android pattern)',
          'ble-manager'
        );

        // Use singleton connection instead of reconnecting
        const device = await this.validateConnection();
        if (!device) {
          throw new Error('No active connection available. Please connect to device first.');
        }

        // Ensure services are discovered
        await this.ensureServicesDiscovered();
        const services = await device.services();

        const smartRingService = services.find((service) =>
          service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
        );

        if (!smartRingService) {
          throw new Error('J Style smart ring service not available on this device');
        }

        // Set up notification listener for battery response
        let notificationSubscription: any = null;
        const timeout = setTimeout(() => {
          if (notificationSubscription) {
            this.logger.debug('⏰ Battery timeout, cleaning up notification', 'ble-manager');
            notificationSubscription.remove();
            notificationSubscription = null;
          }
          reject(new Error('Timeout waiting for battery level response'));
        }, 10000); // 10 second timeout

        notificationSubscription = device.monitorCharacteristicForService(
          smartRingService.uuid,
          SMART_RING_RX_UUID,
          (error, characteristic) => {
            if (error) {
              clearTimeout(timeout);
              if (notificationSubscription) {
                setTimeout(() => {
                  notificationSubscription?.remove();
                }, 100);
              }

              // "Operation was cancelled" is expected during cleanup - don't log as error
              if (error.message && error.message.includes('Operation was cancelled')) {
                this.logger.debug('Battery notification cleanup (expected):', 'ble-manager', {
                  error: error.message,
                });
                // Don't reject for cleanup cancellations - they're expected
                return;
              }

              this.logger.error('Battery notification error:', 'ble-manager', { error });
              reject(error);
              return;
            }

            if (characteristic?.value) {
              try {
                const data = base64.decode(characteristic.value);
                const bytes = new Uint8Array(
                  data.split('').map((char: string) => char.charCodeAt(0))
                );

                this.logger.debug('📥 Battery notification received:', 'ble-manager', {
                  rawBytes: Array.from(bytes)
                    .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                    .join(' '),
                  totalLength: bytes.length,
                  firstByte: `0x${bytes[0]?.toString(16).padStart(2, '0')}`,
                  secondByte: `0x${bytes[1]?.toString(16).padStart(2, '0')}`,
                });

                // Check if this is a battery response (CMD_Get_BatteryLevel = 0x13)
                if (bytes.length >= 2 && bytes[0] === SMART_RING_COMMANDS.CMD_Get_BatteryLevel) {
                  clearTimeout(timeout);

                  // Clean up subscription
                  setTimeout(() => {
                    if (notificationSubscription) {
                      notificationSubscription.remove();
                      notificationSubscription = null;
                    }
                  }, 100);

                  const batteryLevel = bytes[1];

                  // Validate battery level range
                  if (batteryLevel < 0 || batteryLevel > 100) {
                    this.logger.warn(
                      `Unusual battery level: ${batteryLevel}%, using anyway`,
                      'ble-manager'
                    );
                  }

                  this.logger.info(`Battery level: ${batteryLevel}%`, 'ble-manager');
                  resolve(batteryLevel);
                }
                // If not battery response, ignore and continue listening
              } catch {
                // Don't reject on parse errors, just log and continue
                this.logger.debug(
                  'Non-battery notification received, continuing...',
                  'ble-manager'
                );
              }
            }
          }
        );

        // Create battery command (following official Android implementation pattern)
        const command = new Uint8Array(16);
        command[0] = SMART_RING_COMMANDS.CMD_Get_BatteryLevel; // 0x13

        // Calculate CRC exactly like official SDK
        let crc = 0;
        for (let i = 0; i < 15; i++) {
          crc += command[i];
        }
        command[15] = crc & 0xff;

        this.logger.debug('📤 Sending battery command via notification pattern:', 'ble-manager', {
          command: Array.from(command)
            .map((b) => '0x' + b.toString(16).padStart(2, '0'))
            .join(' '),
          crcCalculated: `0x${(crc & 0xff).toString(16).padStart(2, '0')}`,
        });

        // Send command with Android pattern timing (500ms delay like official implementation)
        setTimeout(async () => {
          try {
            const commandBase64 = base64.encode(String.fromCharCode(...command));
            await device.writeCharacteristicWithResponseForService(
              smartRingService.uuid,
              SMART_RING_TX_UUID,
              commandBase64
            );
            this.logger.debug(
              '✅ Battery command sent, waiting for notification...',
              'ble-manager'
            );
          } catch (writeError) {
            clearTimeout(timeout);
            if (notificationSubscription) {
              notificationSubscription.remove();
            }
            this.logger.error('Failed to send battery command:', 'ble-manager', { writeError });
            reject(writeError);
          }
        }, 500); // 500ms delay like official Android implementation
      } catch (error) {
        this.logger.error(`Error in notification-based battery request: ${error}`, 'ble-manager');
        reject(error);
      }
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async getOxygenData(deviceId: string): Promise<SmartRingOxygenData> {
    return new Promise(async (resolve, reject) => {
      try {
        this.logger.info(
          'Requesting oxygen data using notifications (Android pattern)',
          'ble-manager'
        );

        // Use singleton connection instead of reconnecting
        const device = await this.validateConnection();
        if (!device) {
          throw new Error('No active connection available. Please connect to device first.');
        }

        // Ensure services are discovered
        await this.ensureServicesDiscovered();
        const services = await device.services();

        const smartRingService = services.find((service) =>
          service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
        );

        if (!smartRingService) {
          throw new Error('J Style smart ring service not available on this device');
        }

        // Set up notification listener for oxygen response
        let notificationSubscription: any = null;
        const timeout = setTimeout(() => {
          if (notificationSubscription) {
            this.logger.debug('⏰ Oxygen timeout, cleaning up notification', 'ble-manager');
            notificationSubscription.remove();
            notificationSubscription = null;
          }
          reject(new Error('Timeout waiting for oxygen data response'));
        }, 10000); // 10 second timeout

        notificationSubscription = device.monitorCharacteristicForService(
          smartRingService.uuid,
          SMART_RING_RX_UUID,
          (error, characteristic) => {
            if (error) {
              clearTimeout(timeout);
              if (notificationSubscription) {
                setTimeout(() => {
                  notificationSubscription?.remove();
                }, 100);
              }

              // "Operation was cancelled" is expected during cleanup - don't log as error
              if (error.message && error.message.includes('Operation was cancelled')) {
                this.logger.debug('Oxygen notification cleanup (expected):', 'ble-manager', {
                  error: error.message,
                });
                // Don't reject for cleanup cancellations - they're expected
                return;
              }

              this.logger.error('Oxygen notification error:', 'ble-manager', { error });
              reject(error);
              return;
            }

            if (characteristic?.value) {
              try {
                const data = base64.decode(characteristic.value);
                const bytes = new Uint8Array(
                  data.split('').map((char: string) => char.charCodeAt(0))
                );

                this.logger.debug('📥 Oxygen notification received:', 'ble-manager', {
                  rawBytes: Array.from(bytes)
                    .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                    .join(' '),
                  totalLength: bytes.length,
                  firstByte: `0x${bytes[0]?.toString(16).padStart(2, '0')}`,
                  secondByte: `0x${bytes[1]?.toString(16).padStart(2, '0')}`,
                });

                // Check if this is an oxygen response (Oxygen_data = 0x66)
                if (bytes.length >= 2 && bytes[0] === SMART_RING_COMMANDS.Oxygen_data) {
                  clearTimeout(timeout);

                  // Clean up subscription
                  setTimeout(() => {
                    if (notificationSubscription) {
                      notificationSubscription.remove();
                      notificationSubscription = null;
                    }
                  }, 100);

                  // Enhanced debugging for oxygen data parsing
                  this.logger.debug('🔍 Detailed oxygen response analysis:', 'ble-manager', {
                    fullRawData: Array.from(bytes),
                    dataLength: bytes.length,
                    firstFewBytes: Array.from(bytes.slice(0, 10))
                      .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
                      .join(' '),
                    byte1_oxygenLevel: bytes[1],
                    byte2: bytes[2],
                    byte3: bytes[3],
                    possibleOxygenAtByte2: bytes.length > 2 ? bytes[2] : 'N/A',
                    possibleOxygenAtByte3: bytes.length > 3 ? bytes[3] : 'N/A',
                  });

                  // Parse oxygen data following official SDK pattern
                  // Official test app expects automatic SpO2 monitoring data with potential multiple records
                  let oxygenLevel = 0; // Default value

                  // Try different parsing strategies based on data length and format
                  if (bytes.length >= 10) {
                    // Multi-record format similar to heart rate (like official test app handles)
                    this.logger.debug('🔍 Parsing multi-record oxygen format...', 'ble-manager');

                    // Look for valid oxygen records in the response
                    // Format might be similar to heart rate: [CMD][ID1][ID2][YY][MM][DD][HH][mm][SS][SpO2]
                    const recordSize = 10;
                    const numRecords = Math.floor(bytes.length / recordSize);

                    for (let record = 0; record < numRecords; record++) {
                      const offset = record * recordSize;
                      if (offset + 9 < bytes.length) {
                        const potentialSpO2 = bytes[offset + 9]; // Last byte of record
                        if (potentialSpO2 > 80 && potentialSpO2 <= 100) {
                          oxygenLevel = potentialSpO2;
                          this.logger.debug(
                            `📊 Found SpO2 ${oxygenLevel}% in record ${record} at byte[${offset + 9}]`,
                            'ble-manager'
                          );
                          break;
                        }
                      }
                    }
                  }

                  // Fallback: Try simple single-byte positions
                  if (oxygenLevel === 0) {
                    this.logger.debug('🔍 Trying simple single-byte positions...', 'ble-manager');
                    for (let i = 1; i < Math.min(bytes.length, 10); i++) {
                      if (bytes[i] > 80 && bytes[i] <= 100) {
                        oxygenLevel = bytes[i];
                        this.logger.debug(
                          `🔄 Using oxygen level ${oxygenLevel}% from byte[${i}]`,
                          'ble-manager'
                        );
                        break;
                      }
                    }
                  }

                  // Final fallback: Use byte[1] even if it's 0 (to show the actual response)
                  if (oxygenLevel === 0 && bytes.length > 1) {
                    oxygenLevel = bytes[1];
                    this.logger.debug('⚠️ Using byte[1] value as fallback', 'ble-manager');
                  }

                  // Validate oxygen level range (typical SpO2: 85-100%)
                  if (oxygenLevel < 70 || oxygenLevel > 100) {
                    this.logger.warn(
                      `Unusual oxygen level: ${oxygenLevel}%, using anyway`,
                      'ble-manager'
                    );
                  }

                  const result = {
                    oxygenLevel,
                    timestamp: new Date(),
                    rawData: Array.from(bytes),
                  };

                  this.logger.info(`Oxygen level: ${oxygenLevel}%`, 'ble-manager');
                  resolve(result);
                } else {
                  // This is a notification but not an oxygen response
                  this.logger.debug('💭 Received non-oxygen notification:', 'ble-manager', {
                    commandType: `0x${bytes[0]?.toString(16).padStart(2, '0')}`,
                    expectedOxygenCommand: `0x${SMART_RING_COMMANDS.Oxygen_data.toString(16).padStart(2, '0')}`,
                    dataLength: bytes.length,
                    firstFewBytes: Array.from(bytes.slice(0, 5))
                      .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
                      .join(' '),
                  });
                }
                // If not oxygen response, ignore and continue listening
              } catch {
                // Don't reject on parse errors, just log and continue
                this.logger.debug('Non-oxygen notification received, continuing...', 'ble-manager');
              }
            }
          }
        );

        // Create oxygen command following official SDK pattern: BleSDK.Oxygen_data(ModeStart, "")
        // From official test app: uses ModeStart=0x00 and empty dateOfLastData
        const command = new Uint8Array(16);
        command[0] = SMART_RING_COMMANDS.Oxygen_data; // 0x66 (DeviceConst.Oxygen_data)
        command[1] = 0x00; // ModeStart (0x00 = start getting data)

        // insertDateValue for empty dateOfLastData - sets current date/time or zeros
        // Based on heart rate pattern, leave date bytes as 0x00 for "read latest"
        command[2] = 0x00; // Year offset
        command[3] = 0x00; // Month
        command[4] = 0x00; // Day
        command[5] = 0x00; // Hour
        command[6] = 0x00; // Minute
        command[7] = 0x00; // Second

        // Calculate CRC exactly like official SDK crcValue()
        let crc = 0;
        for (let i = 0; i < 15; i++) {
          crc += command[i];
        }
        command[15] = crc & 0xff;

        this.logger.debug('📤 Sending oxygen command via notification pattern:', 'ble-manager', {
          command: Array.from(command)
            .map((b) => '0x' + b.toString(16).padStart(2, '0'))
            .join(' '),
          crcCalculated: `0x${(crc & 0xff).toString(16).padStart(2, '0')}`,
          commandDetails: {
            byte0_command: `0x${command[0].toString(16).padStart(2, '0')}`,
            byte1_action: `0x${command[1].toString(16).padStart(2, '0')}`,
            byte15_crc: `0x${command[15].toString(16).padStart(2, '0')}`,
          },
        });

        // Send command with Android pattern timing (500ms delay like official implementation)
        setTimeout(async () => {
          try {
            const commandBase64 = base64.encode(String.fromCharCode(...command));
            await device.writeCharacteristicWithResponseForService(
              smartRingService.uuid,
              SMART_RING_TX_UUID,
              commandBase64
            );
            this.logger.debug('✅ Oxygen command sent, waiting for notification...', 'ble-manager');
          } catch (writeError) {
            clearTimeout(timeout);
            if (notificationSubscription) {
              notificationSubscription.remove();
            }
            this.logger.error('Failed to send oxygen command:', 'ble-manager', { writeError });
            reject(writeError);
          }
        }, 500); // 500ms delay like official Android implementation
      } catch (error) {
        this.logger.error(`Error in notification-based oxygen request: ${error}`, 'ble-manager');
        reject(error);
      }
    });
  }

  async getTotalActivityData(deviceId: string): Promise<SmartRingActivityData> {
    try {
      this.logger.info('Requesting total activity data from J Style smart ring', 'ble-manager');

      const device = await this.bleManager.connectToDevice(deviceId);
      await device.discoverAllServicesAndCharacteristics();
      const services = await device.services();

      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        throw new Error('J Style smart ring service not available on this device');
      }

      // Send total data command
      const command = new Uint8Array([SMART_RING_COMMANDS.CMD_Get_TotalData]);
      const commandBase64 = base64.encode(String.fromCharCode(...command));

      await device.writeCharacteristicWithResponseForService(
        smartRingService.uuid,
        SMART_RING_TX_UUID,
        commandBase64
      );

      // Read response
      const response = await device.readCharacteristicForService(
        smartRingService.uuid,
        SMART_RING_RX_UUID
      );

      if (!response.value) {
        throw new Error('No activity response received from smart ring');
      }

      const data = base64.decode(response.value);
      const bytes = new Uint8Array(data.split('').map((char: string) => char.charCodeAt(0)));

      this.logger.info('Total activity data received', 'ble-manager', {
        rawData: Array.from(bytes),
      });
      return {
        totalSteps: bytes.length > 2 ? bytes[1] | (bytes[2] << 8) : null,
        timestamp: new Date(),
        rawData: Array.from(bytes),
      };
    } catch (error) {
      this.logger.error(`Error getting total activity data: ${error}`, 'ble-manager');
      throw error;
    }
  }

  async setDeviceTime(deviceId: string, time?: Date): Promise<void> {
    try {
      const targetTime = time || new Date();
      this.logger.info('Setting device time on J Style smart ring', 'ble-manager', {
        time: targetTime,
      });

      const device = await this.bleManager.connectToDevice(deviceId);
      await device.discoverAllServicesAndCharacteristics();
      const services = await device.services();

      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        throw new Error('J Style smart ring service not available on this device');
      }

      // Create time command (CMD_SET_TIME with timestamp data)
      const command = new Uint8Array(7);
      command[0] = SMART_RING_COMMANDS.CMD_SET_TIME;
      command[1] = targetTime.getFullYear() - 2000; // Year offset from 2000
      command[2] = targetTime.getMonth() + 1; // Month (1-based)
      command[3] = targetTime.getDate(); // Day
      command[4] = targetTime.getHours(); // Hour
      command[5] = targetTime.getMinutes(); // Minute
      command[6] = targetTime.getSeconds(); // Second

      const commandBase64 = base64.encode(String.fromCharCode(...command));

      await device.writeCharacteristicWithResponseForService(
        smartRingService.uuid,
        SMART_RING_TX_UUID,
        commandBase64
      );

      this.logger.info('Device time set successfully', 'ble-manager');
    } catch (error) {
      this.logger.error(`Error setting device time: ${error}`, 'ble-manager');
      throw error;
    }
  }

  private parseSmartRingHeartRateResponse(value: string): SmartRingHeartRateResponse {
    try {
      const data = base64.decode(value);
      this.logger.debug('Raw J Style response data:', 'ble-manager', { rawData: data });

      // Convert string to byte array
      const bytes = new Uint8Array(data.split('').map((char: string) => char.charCodeAt(0)));

      // Add detailed byte debugging
      this.logger.debug('Parsed bytes (first 20):', 'ble-manager', {
        byteArray: Array.from(bytes.slice(0, 20))
          .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
          .join(' '),
        totalLength: bytes.length,
        firstByte: `0x${bytes[0]?.toString(16).padStart(2, '0')}`,
        secondByte: `0x${bytes[1]?.toString(16).padStart(2, '0')}`,
      });

      if (bytes.length < 10) {
        throw new Error('Invalid response length from smart ring');
      }

      const commandType = bytes[0];

      if (commandType === SMART_RING_COMMANDS.CMD_Get_HeartData) {
        return this.parseContinuousHeartRateData(bytes);
      } else if (commandType === SMART_RING_COMMANDS.GET_SINGLE_HEART_RATE_DATA) {
        return this.parseSingleHeartRateData(bytes);
      } else {
        throw new Error(`Unknown command type: 0x${commandType.toString(16)}`);
      }
    } catch (error) {
      this.logger.error(`Error parsing smart ring heart rate response: ${error}`, 'ble-manager');
      throw error;
    }
  }

  private parseContinuousHeartRateData(bytes: Uint8Array): SmartRingContinuousHeartRateData {
    // Format: 0x54 ID1 ID2 YY MM DD HH mm SS SD1 SD2 SD3 ... SD15
    // Minimum 21 bytes for complete data
    if (bytes.length < 21) {
      throw new Error('Insufficient data for continuous heart rate parsing');
    }

    // Extract data ID (ID1 ID2: high-order bit last)
    const dataId = bytes[1] | (bytes[2] << 8);

    // Extract timestamp (YY MM DD HH mm SS)
    const year = 2000 + bytes[3]; // YY is offset from 2000
    const month = bytes[4] - 1; // Month is 1-based in data, 0-based in Date
    const day = bytes[5];
    const hour = bytes[6];
    const minute = bytes[7];
    const second = bytes[8];

    const timestamp = new Date(year, month, day, hour, minute, second);

    // Extract heart rate readings (SD1 SD2 SD3 ... SD15)
    const readings = [];
    for (let i = 0; i < 15; i++) {
      const byteIndex = 9 + i;
      if (byteIndex < bytes.length) {
        const heartRate = bytes[byteIndex];
        if (heartRate > 0 && heartRate <= 220) {
          // Valid heart rate range
          readings.push({
            heartRate,
            timestamp: new Date(timestamp.getTime() + i * 60 * 1000), // Each reading is 1 minute apart
          });
        }
      }
    }

    return {
      commandType: 0x54,
      dataId,
      timestamp,
      readings,
    };
  }

  private parseSingleHeartRateData(bytes: Uint8Array): SmartRingSingleHeartRateData {
    // Official SDK format: Multiple 10-byte records
    // Each record: [CMD][ID1][ID2][YY][MM][DD][HH][mm][SS][HR]

    const count = 10; // 10 bytes per record
    const length = bytes.length;
    const size = Math.floor(length / count);

    this.logger.debug('Parsing multiple heart rate records:', 'ble-manager', {
      totalBytes: length,
      recordSize: count,
      numberOfRecords: size,
    });

    if (size === 0) {
      throw new Error('No complete heart rate records found');
    }

    // Process the first complete record (most recent)
    let recordIndex = 0;

    // Find the first valid record (sometimes first few bytes might be metadata)
    for (let i = 0; i < size; i++) {
      const offset = i * count;
      if (bytes[offset] === 0x55) {
        // Valid heart rate command
        recordIndex = i;
        break;
      }
    }

    const offset = recordIndex * count;

    // Extract data ID (ID1 ID2: high-order bit last)
    const dataId = bytes[offset + 1] | (bytes[offset + 2] << 8);

    // Extract timestamp using BCD format (like official SDK)
    // ByteToHexString converts byte to 2-digit hex string
    const yearHex = this.byteToHexString(bytes[offset + 3]);
    const monthHex = this.byteToHexString(bytes[offset + 4]);
    const dayHex = this.byteToHexString(bytes[offset + 5]);
    const hourHex = this.byteToHexString(bytes[offset + 6]);
    const minuteHex = this.byteToHexString(bytes[offset + 7]);
    const secondHex = this.byteToHexString(bytes[offset + 8]);

    // Convert BCD hex strings to numbers
    const year = 2000 + parseInt(yearHex, 16);
    const month = parseInt(monthHex, 16) - 1; // Month is 1-based in data, 0-based in Date
    const day = parseInt(dayHex, 16);
    const hour = parseInt(hourHex, 16);
    const minute = parseInt(minuteHex, 16);
    const second = parseInt(secondHex, 16);

    // Add debugging for date parsing (matching official SDK)
    this.logger.debug('BCD Date parsing debug:', 'ble-manager', {
      rawBytes: Array.from(bytes.slice(offset + 3, offset + 9))
        .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
        .join(' '),
      yearHex,
      monthHex,
      dayHex,
      hourHex,
      minuteHex,
      secondHex,
      calculatedYear: year,
      calculatedMonth: month + 1, // Show 1-based for debugging
      calculatedDay: day,
      calculatedHour: hour,
      calculatedMinute: minute,
      calculatedSecond: second,
    });

    const timestamp = new Date(year, month, day, hour, minute, second);

    // Extract heart rate (9th byte in the record)
    const heartRate = bytes[offset + 9];

    if (heartRate <= 0 || heartRate > 220) {
      this.logger.warn(`Unusual heart rate value: ${heartRate}, using anyway`, 'ble-manager');
    }

    this.logger.debug('Parsed heart rate record:', 'ble-manager', {
      recordIndex,
      dataId,
      heartRate,
      timestamp: timestamp.toISOString(),
      totalRecordsAvailable: size,
    });

    return {
      commandType: 0x55,
      dataId,
      timestamp,
      heartRate,
    };
  }

  // Helper method to convert byte to hex string (matching official SDK ByteToHexString)
  private byteToHexString(byte: number): string {
    const hex = byte.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }

  // Legacy method - keep for backward compatibility
  private parseJStyleHeartRateData(value: string): HeartRateData | null {
    try {
      const data = base64.decode(value);
      this.logger.debug('Raw J Style data (legacy):', 'ble-manager', { rawData: data });

      // Parse J Style smart ring data format
      const bytes = new Uint8Array(data.split('').map((char: string) => char.charCodeAt(0)));

      if (bytes.length < 2) {
        this.logger.warn('Invalid J Style heart rate data length', 'ble-manager');
        return null;
      }

      // Try to parse as smart ring protocol first
      try {
        const smartRingResponse = this.parseSmartRingHeartRateResponse(value);

        // Convert to legacy format
        if (smartRingResponse.commandType === 0x55) {
          const singleData = smartRingResponse as SmartRingSingleHeartRateData;
          return {
            heartRate: singleData.heartRate,
            sensorContact: true,
            timestamp: singleData.timestamp,
          };
        } else if (smartRingResponse.commandType === 0x54) {
          const continuousData = smartRingResponse as SmartRingContinuousHeartRateData;
          const latestReading = continuousData.readings[continuousData.readings.length - 1];
          if (latestReading) {
            return {
              heartRate: latestReading.heartRate,
              sensorContact: true,
              timestamp: latestReading.timestamp,
            };
          }
        }
      } catch (parseError) {
        // Fall back to simple parsing if smart ring protocol fails
        this.logger.debug('Smart ring parsing failed, using legacy method:', 'ble-manager', {
          parseError,
        });
      }

      // Legacy parsing (original implementation)
      let heartRate: number;

      if (bytes.length >= 2) {
        heartRate = bytes[1]; // Most common: heart rate in second byte

        if (heartRate === 0 || heartRate > 220) {
          heartRate = bytes[0]; // Try first byte
        }

        if (heartRate === 0 || heartRate > 220) {
          heartRate = bytes[0] | (bytes[1] << 8); // Try 16-bit parsing
        }
      } else {
        heartRate = bytes[0];
      }

      // Validate heart rate range
      if (heartRate < 30 || heartRate > 220) {
        this.logger.warn(`Invalid heart rate value from J Style ring: ${heartRate}`, 'ble-manager');
        return null;
      }

      return {
        heartRate,
        sensorContact: true,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error parsing J Style heart rate data: ${error}`, 'ble-manager');
      return null;
    }
  }

  async requestSingleHeartRateDataWithNotification(
    deviceId: string,
    params: HeartRateRequestParams
  ): Promise<SmartRingHeartRateResponse> {
    return new Promise(async (resolve, reject) => {
      try {
        this.logger.info('Requesting single heart rate data using notifications:', 'ble-manager', {
          deviceId,
          params,
        });

        // Use singleton connection instead of reconnecting
        const device = await this.validateConnection();
        if (!device) {
          throw new Error('No active connection available. Please connect to device first.');
        }

        // Ensure services are discovered
        await this.ensureServicesDiscovered();
        const services = await device.services();

        const smartRingService = services.find((service) =>
          service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
        );

        if (!smartRingService) {
          throw new Error('J Style smart ring service not available on this device');
        }

        // Set up notification listener for response
        let notificationSubscription: any = null;
        const timeout = setTimeout(() => {
          if (notificationSubscription) {
            this.logger.debug(
              '⏰ Timeout reached, cleaning up notification subscription',
              'ble-manager'
            );
            notificationSubscription.remove();
            notificationSubscription = null;
          }
          reject(new Error('Timeout waiting for heart rate data response'));
        }, 10000); // 10 second timeout

        notificationSubscription = device.monitorCharacteristicForService(
          smartRingService.uuid,
          SMART_RING_RX_UUID,
          (error, characteristic) => {
            if (error) {
              clearTimeout(timeout);
              if (notificationSubscription) {
                // Add small delay to avoid immediate cancellation issues
                setTimeout(() => {
                  notificationSubscription?.remove();
                }, 100);
              }

              // "Operation was cancelled" is expected during cleanup - don't log as error
              if (error.message && error.message.includes('Operation was cancelled')) {
                this.logger.debug('Heart rate notification cleanup (expected):', 'ble-manager', {
                  error: error.message,
                });
                // Don't reject for cleanup cancellations - they're expected
                return;
              }

              this.logger.error('Notification error:', 'ble-manager', { error });
              reject(error);
              return;
            }

            if (characteristic?.value) {
              clearTimeout(timeout);

              try {
                this.logger.debug('✅ Received notification response', 'ble-manager', {
                  valueLength: characteristic.value.length,
                });

                const response = this.parseSmartRingHeartRateResponse(characteristic.value);

                // Clean up subscription after successful response with small delay
                setTimeout(() => {
                  if (notificationSubscription) {
                    notificationSubscription.remove();
                    notificationSubscription = null;
                  }
                }, 100);

                resolve(response);
              } catch (parseError) {
                // Clean up on parse error
                setTimeout(() => {
                  if (notificationSubscription) {
                    notificationSubscription.remove();
                    notificationSubscription = null;
                  }
                }, 100);

                this.logger.error('Error parsing notification response:', 'ble-manager', {
                  parseError,
                });
                reject(parseError);
              }
            }
          }
        );

        // Create command
        let actionCode: number;
        switch (params.action) {
          case 'delete_all':
            actionCode = HEART_RATE_ACTIONS.DELETE_ALL;
            break;
          case 'read_latest':
            actionCode = HEART_RATE_ACTIONS.READ_LATEST;
            break;
          case 'read_at_location':
            actionCode = HEART_RATE_ACTIONS.READ_AT_LOCATION;
            break;
          case 'continue_from_last':
            actionCode = HEART_RATE_ACTIONS.CONTINUE_FROM_LAST;
            break;
          default:
            actionCode = HEART_RATE_ACTIONS.READ_LATEST;
        }

        const command = createHeartRateCommand(
          SMART_RING_COMMANDS.GET_SINGLE_HEART_RATE_DATA,
          actionCode
        );

        this.logger.debug('📤 Sending command via notification method:', 'ble-manager', {
          command: Array.from(command)
            .map((b) => '0x' + b.toString(16).padStart(2, '0'))
            .join(' '),
        });

        // Send command
        const commandBase64 = base64.encode(String.fromCharCode(...command));
        await device.writeCharacteristicWithResponseForService(
          smartRingService.uuid,
          SMART_RING_TX_UUID,
          commandBase64
        );

        this.logger.debug('✅ Command sent, waiting for notification...', 'ble-manager');
      } catch (error) {
        this.logger.error(
          `Error in notification-based heart rate request: ${error}`,
          'ble-manager'
        );
        reject(error);
      }
    });
  }

  async requestSingleHeartRateDataDirect(
    deviceId: string,
    params: HeartRateRequestParams
  ): Promise<SmartRingHeartRateResponse> {
    try {
      this.logger.info('Requesting single heart rate data using direct read:', 'ble-manager', {
        deviceId,
        params,
      });

      // Use singleton connection
      const device = await this.validateConnection();
      await this.ensureServicesDiscovered();
      const services = await device.services();

      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        throw new Error('J Style smart ring service not available on this device');
      }

      // Create command
      let actionCode: number;
      switch (params.action) {
        case 'delete_all':
          actionCode = HEART_RATE_ACTIONS.DELETE_ALL;
          break;
        case 'read_latest':
          actionCode = HEART_RATE_ACTIONS.READ_LATEST;
          break;
        case 'read_at_location':
          actionCode = HEART_RATE_ACTIONS.READ_AT_LOCATION;
          break;
        case 'continue_from_last':
          actionCode = HEART_RATE_ACTIONS.CONTINUE_FROM_LAST;
          break;
        default:
          actionCode = HEART_RATE_ACTIONS.READ_LATEST;
      }

      const command = createHeartRateCommand(
        SMART_RING_COMMANDS.GET_SINGLE_HEART_RATE_DATA,
        actionCode
      );

      this.logger.debug('📤 Sending command via direct method:', 'ble-manager', {
        command: Array.from(command)
          .map((b) => '0x' + b.toString(16).padStart(2, '0'))
          .join(' '),
      });

      // Send command to TX characteristic
      const commandBase64 = base64.encode(String.fromCharCode(...command));
      await device.writeCharacteristicWithResponseForService(
        smartRingService.uuid,
        SMART_RING_TX_UUID,
        commandBase64
      );

      this.logger.debug('✅ Command sent, attempting direct read...', 'ble-manager');

      // Wait a moment for the device to process the command
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Try direct read from RX characteristic
      this.logger.debug('📥 Attempting direct read from RX characteristic...', 'ble-manager');

      const response = await device.readCharacteristicForService(
        smartRingService.uuid,
        SMART_RING_RX_UUID
      );

      if (!response.value) {
        throw new Error('No response received from smart ring via direct read');
      }

      this.logger.debug('✅ Direct read successful!', 'ble-manager', {
        valueLength: response.value.length,
      });

      return this.parseSmartRingHeartRateResponse(response.value);
    } catch (error) {
      this.logger.error(`Error in direct heart rate request: ${error}`, 'ble-manager');
      throw error;
    }
  }

  // Live measurement methods (based on official SDK MeasurementWithType)
  async startLiveMeasurement(
    deviceId: string,
    params: LiveMeasurementParams
  ): Promise<LiveMeasurementResponse> {
    try {
      this.logger.info('Starting live measurement:', 'ble-manager', { params });

      // Check connection state before proceeding
      this.logger.debug('Connection state before live measurement:', 'ble-manager', {
        hasConnectedDevice: !!this.connectedDevice,
        connectedDeviceId: this.connectedDeviceId,
        requestedDeviceId: deviceId,
        isConnecting: this.isConnecting,
      });

      // Use singleton connection
      const device = await this.validateConnection();

      this.logger.debug('Connection validated, ensuring services...', 'ble-manager');
      await this.ensureServicesDiscovered();
      const services = await device.services();

      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        throw new Error('J Style smart ring service not available on this device');
      }

      // Create live measurement command using official SDK pattern
      const command = createLiveMeasurementCommand(
        params.measurementType as 2 | 3, // Only heart rate (2) and oxygen (3) supported
        params.durationSeconds,
        true // true = start measurement
      );

      this.logger.debug('📤 Sending live measurement START command:', 'ble-manager', {
        command: Array.from(command)
          .map((b: number) => '0x' + b.toString(16).padStart(2, '0'))
          .join(' '),
        measurementType: params.measurementType,
        duration: params.durationSeconds,
        commandStructure: {
          byte0_command: `0x${command[0].toString(16).padStart(2, '0')}`,
          byte1_type: `0x${command[1].toString(16).padStart(2, '0')}`,
          byte2_start: `0x${command[2].toString(16).padStart(2, '0')}`,
          byte3_duration_low: `0x${command[3].toString(16).padStart(2, '0')}`,
          byte4_duration_high: `0x${command[4].toString(16).padStart(2, '0')}`,
          byte15_crc: `0x${command[15].toString(16).padStart(2, '0')}`,
        },
      });

      // Send command
      const commandBase64 = base64.encode(String.fromCharCode(...command));
      await device.writeCharacteristicWithResponseForService(
        smartRingService.uuid,
        SMART_RING_TX_UUID,
        commandBase64
      );

      this.logger.info('Live measurement started successfully', 'ble-manager');

      // Return a response indicating measurement has started
      return {
        commandType: 0x28,
        measurementType: params.measurementType,
        isComplete: false,
        timestamp: new Date(),
        data: {},
        rawData: Array.from(command),
      };
    } catch (error) {
      this.logger.error(`Error starting live measurement: ${error}`, 'ble-manager');
      throw error;
    }
  }

  async stopLiveMeasurement(deviceId: string, measurementType: 2 | 3): Promise<void> {
    try {
      this.logger.info('Stopping live measurement:', 'ble-manager', { measurementType });

      // Use singleton connection
      const device = await this.validateConnection();
      await this.ensureServicesDiscovered();
      const services = await device.services();

      const smartRingService = services.find((service) =>
        service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
      );

      if (!smartRingService) {
        throw new Error('J Style smart ring service not available on this device');
      }

      // Create stop command using official SDK pattern
      const command = createLiveMeasurementCommand(
        measurementType,
        0, // Duration 0 for stop command
        false // false = stop measurement
      );

      this.logger.debug('📤 Sending live measurement STOP command:', 'ble-manager', {
        command: Array.from(command)
          .map((b: number) => '0x' + b.toString(16).padStart(2, '0'))
          .join(' '),
        measurementType,
        commandStructure: {
          byte0_command: `0x${command[0].toString(16).padStart(2, '0')}`,
          byte1_type: `0x${command[1].toString(16).padStart(2, '0')}`,
          byte2_stop: `0x${command[2].toString(16).padStart(2, '0')}`,
          byte15_crc: `0x${command[15].toString(16).padStart(2, '0')}`,
        },
      });

      // Send stop command
      const commandBase64 = base64.encode(String.fromCharCode(...command));
      await device.writeCharacteristicWithResponseForService(
        smartRingService.uuid,
        SMART_RING_TX_UUID,
        commandBase64
      );

      this.logger.info('Live measurement stopped successfully', 'ble-manager');
    } catch (error) {
      this.logger.error(`Error stopping live measurement: ${error}`, 'ble-manager');
      throw error;
    }
  }

  monitorLiveMeasurement(
    deviceId: string,
    callback: (
      error: BleError | null,
      data: LiveMeasurementResponse | LiveMeasurementStopResponse | null
    ) => void
  ): () => void {
    try {
      this.logger.info('Starting live measurement monitoring:', 'ble-manager', { deviceId });

      let notificationSubscription: any = null;

      // Set up the monitoring asynchronously with timeout
      const setupWithTimeout = Promise.race([
        this.setupLiveMeasurementMonitoring(deviceId, callback),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Live measurement monitoring setup timeout')), 5000)
        ),
      ]);

      setupWithTimeout
        .then((subscription) => {
          notificationSubscription = subscription;
          this.logger.debug(
            '✅ Live measurement monitoring setup completed successfully',
            'ble-manager'
          );
        })
        .catch((error) => {
          this.logger.error('Failed to set up live measurement monitoring:', 'ble-manager', {
            error,
          });
          callback(error, null);
        });

      const unsubscribe = () => {
        this.logger.info('Live measurement monitoring stopped', 'ble-manager');
        if (notificationSubscription) {
          notificationSubscription.remove();
          notificationSubscription = null;
        }
      };

      return unsubscribe;
    } catch (error) {
      this.logger.error(`Error setting up live measurement monitoring: ${error}`, 'ble-manager');
      callback(error as BleError, null);
      return () => {};
    }
  }

  private async setupLiveMeasurementMonitoring(
    deviceId: string,
    callback: (
      error: BleError | null,
      data: LiveMeasurementResponse | LiveMeasurementStopResponse | null
    ) => void
  ): Promise<any> {
    // Use existing connection directly without re-validation to avoid disrupting it
    if (!this.connectedDevice || !this.connectedDeviceId) {
      throw new Error('No device connected for live measurement monitoring');
    }

    // Check connection status without calling methods that might disrupt it
    const isConnected = await this.connectedDevice.isConnected();
    if (!isConnected) {
      throw new Error('Device is not connected for live measurement monitoring');
    }

    // Get services (they should already be discovered from connection)
    const services = await this.connectedDevice.services();

    const smartRingService = services.find((service) =>
      service.uuid.toLowerCase().includes(SMART_RING_SERVICE_UUID.toLowerCase())
    );

    if (!smartRingService) {
      this.logger.error('Smart ring service not found for live monitoring', 'ble-manager', {
        availableServices: services.map((s) => s.uuid),
      });
      throw new Error('J Style smart ring service not available on this device');
    }

    this.logger.debug('🔄 Setting up live measurement notification monitoring...', 'ble-manager', {
      serviceUUID: smartRingService.uuid,
      deviceConnected: isConnected,
    });

    // Set up notification listener for live measurement responses
    const notificationSubscription = this.connectedDevice.monitorCharacteristicForService(
      smartRingService.uuid,
      SMART_RING_RX_UUID,
      (error: BleError | null, characteristic: any) => {
        if (error) {
          // "Operation was cancelled" is expected during cleanup - don't log as error
          if (error.message && error.message.includes('Operation was cancelled')) {
            this.logger.debug('Live measurement notification cleanup (expected):', 'ble-manager', {
              error: error.message,
            });
            return;
          }

          this.logger.error('Live measurement notification error:', 'ble-manager', { error });
          callback(error, null);
          return;
        }

        if (characteristic?.value) {
          try {
            const data = base64.decode(characteristic.value);
            const bytes = new Uint8Array(data.split('').map((char: string) => char.charCodeAt(0)));

            this.logger.debug('📥 Live measurement notification received:', 'ble-manager', {
              rawBytes: Array.from(bytes)
                .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                .join(' '),
              totalLength: bytes.length,
              firstByte: `0x${bytes[0]?.toString(16).padStart(2, '0')}`,
            });

            // Check for live measurement responses and related notifications
            if (bytes.length >= 2) {
              // Check for direct MeasurementWithType responses (0x28)
              if (bytes[0] === SMART_RING_COMMANDS.MeasurementWithType) {
                const liveMeasurementData = this.parseLiveMeasurementResponse(bytes);
                if (liveMeasurementData) {
                  this.logger.info('📊 Live measurement response received:', 'ble-manager', {
                    measurementType: liveMeasurementData.measurementType,
                    isComplete: liveMeasurementData.isComplete,
                    hasValue: liveMeasurementData.data?.value !== null,
                    data: liveMeasurementData.data,
                  });
                  callback(null, liveMeasurementData);
                }
              }
              // Check for potential measurement callback notifications (based on official SDK constants)
              else if (bytes[0] === 0x74 || bytes[0] === 0x75) {
                // 0x74 = MeasurementHeartCallback, 0x75 = MeasurementOxygenCallback (from official SDK)
                this.logger.info('🎯 Received measurement callback notification:', 'ble-manager', {
                  callbackType: bytes[0] === 0x74 ? 'HeartCallback' : 'OxygenCallback',
                  rawBytes: Array.from(bytes.slice(0, 10))
                    .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
                    .join(' '),
                });

                // Try to parse this as measurement data
                const measurementData = this.parseCallbackMeasurementData(bytes);
                if (measurementData) {
                  callback(null, measurementData);
                }
              }
              // Check for specific command types that might contain measurement data
              else if (
                bytes[0] === 0x86 ||
                bytes[0] === 0x87 ||
                bytes[0] === 0x13 ||
                bytes[0] === 0x66
              ) {
                // Common commands seen during measurements:
                // 0x86, 0x87 = potential measurement notifications
                // 0x13 = battery level (might accompany measurements)
                // 0x66 = oxygen data command
                this.logger.info(
                  '🎯 Specific measurement-related command detected:',
                  'ble-manager',
                  {
                    commandType: `0x${bytes[0].toString(16).padStart(2, '0')}`,
                    commandName: this.getCommandName(bytes[0]),
                    fullLength: bytes.length,
                    allBytes: Array.from(bytes.slice(0, Math.min(bytes.length, 16)))
                      .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
                      .join(' '),
                    possibleValues: this.extractPossibleMeasurementValues(bytes),
                  }
                );

                // Try specialized parsing for known commands
                const extractedData = this.parseSpecificMeasurementCommand(bytes);
                if (extractedData) {
                  this.logger.info(
                    '🎯 Successfully parsed measurement from specific command!',
                    'ble-manager',
                    { extractedData }
                  );
                  callback(null, extractedData);
                }
              }
              // Check for other potential measurement notifications
              else {
                this.logger.info(
                  '🔔 Unknown notification during live measurement - analyzing for data:',
                  'ble-manager',
                  {
                    commandType: `0x${bytes[0].toString(16).padStart(2, '0')}`,
                    fullLength: bytes.length,
                    allBytes: Array.from(bytes.slice(0, Math.min(bytes.length, 16)))
                      .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
                      .join(' '),
                    possibleValues: this.extractPossibleMeasurementValues(bytes),
                  }
                );

                // Try to extract measurement values from any notification
                const extractedData = this.tryExtractMeasurementFromAnyResponse(bytes);
                if (extractedData) {
                  this.logger.info(
                    '🎯 Found measurement data in unknown notification!',
                    'ble-manager',
                    { extractedData }
                  );
                  callback(null, extractedData);
                }
              }
            }
            // If not live measurement response, ignore and continue listening
          } catch {
            this.logger.debug(
              'Non-live-measurement notification received, continuing...',
              'ble-manager'
            );
          }
        }
      }
    );

    this.logger.debug('✅ Live measurement monitoring set up successfully', 'ble-manager');
    return notificationSubscription;
  }

  private parseLiveMeasurementResponse(bytes: Uint8Array): LiveMeasurementResponse | null {
    try {
      // Parse live measurement response following official SDK pattern
      // The official SDK uses callback-based responses with specific callback types

      if (bytes.length < 3) {
        this.logger.warn('Insufficient data for live measurement response', 'ble-manager');
        return null;
      }

      const commandType = bytes[0]; // Should be 0x28 for MeasurementWithType
      const measurementType = bytes[1] as 2 | 3; // 2 = heart rate, 3 = oxygen
      const status = bytes[2]; // Status or data byte

      this.logger.debug(
        '🔍 Parsing live measurement response (official SDK pattern):',
        'ble-manager',
        {
          commandType: `0x${commandType.toString(16).padStart(2, '0')}`,
          measurementType,
          status,
          fullDataLength: bytes.length,
          allBytes: Array.from(bytes.slice(0, Math.min(bytes.length, 16)))
            .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
            .join(' '),
        }
      );

      // Parse measurement data following official SDK pattern
      let measurementValue = null;
      let isComplete = false;
      let callbackType = '';

      if (measurementType === 2) {
        // Heart rate measurement
        this.logger.debug('🫀 Parsing heart rate data (official SDK pattern)...', 'ble-manager');

        // Official SDK callback: MeasurementHeartCallback="74"
        callbackType = 'MeasurementHeartCallback';

        // Try multiple parsing strategies for heart rate value
        for (let i = 2; i < Math.min(bytes.length, 10); i++) {
          if (bytes[i] > 30 && bytes[i] <= 220) {
            measurementValue = bytes[i];
            this.logger.debug(
              `Found heart rate at byte[${i}]: ${measurementValue} BPM`,
              'ble-manager'
            );
            break;
          }
        }

        // Check completion status following official SDK pattern
        // 0x00 = measurement started/in progress
        // Valid measurement value indicates completion in most cases
        isComplete = measurementValue !== null || status === 0x01 || status === 0x02;

        if (measurementValue !== null) {
          this.logger.info(
            `🫀 Live heart rate (official pattern): ${measurementValue} BPM`,
            'ble-manager'
          );
        }
      } else if (measurementType === 3) {
        // Oxygen measurement
        this.logger.debug('🩸 Parsing oxygen data (official SDK pattern)...', 'ble-manager');

        // Official SDK callback: MeasurementOxygenCallback="75"
        callbackType = 'MeasurementOxygenCallback';

        // Try multiple parsing strategies for oxygen value
        for (let i = 2; i < Math.min(bytes.length, 10); i++) {
          if (bytes[i] >= 80 && bytes[i] <= 100) {
            measurementValue = bytes[i];
            this.logger.debug(`Found oxygen at byte[${i}]: ${measurementValue}%`, 'ble-manager');
            break;
          }
        }

        // Check completion status following official SDK pattern
        isComplete = measurementValue !== null || status === 0x01 || status === 0x02;

        if (measurementValue !== null) {
          this.logger.info(
            `🩸 Live oxygen (official pattern): ${measurementValue}%`,
            'ble-manager'
          );
        }
      }

      // Return data in official SDK format: Map<String, Object> structure
      const dataMap: any = {};
      if (measurementValue !== null) {
        // Official SDK uses specific keys like "heartValue"
        const valueKey = measurementType === 2 ? 'heartValue' : 'oxygenValue';
        dataMap[valueKey] = measurementValue.toString();
      }

      return {
        commandType: 0x28,
        measurementType,
        isComplete,
        timestamp: new Date(),
        data: {
          value: measurementValue,
          status,
          callbackType, // Official SDK callback type
          dataMap, // Official SDK data structure
          officialSDKFormat: {
            DataType: callbackType,
            Data: dataMap,
            End: isComplete,
          },
          rawBytes: Array.from(bytes),
        },
        rawData: Array.from(bytes),
      };
    } catch (error) {
      this.logger.error('Error parsing live measurement response:', 'ble-manager', { error });
      return null;
    }
  }

  private parseCallbackMeasurementData(bytes: Uint8Array): LiveMeasurementResponse | null {
    try {
      // Parse measurement callback notifications following official SDK pattern
      // Official SDK callbacks: 0x74 = MeasurementHeartCallback, 0x75 = MeasurementOxygenCallback

      if (bytes.length < 3) {
        return null;
      }

      const callbackType = bytes[0]; // 0x74 or 0x75
      const measurementType = callbackType === 0x74 ? 2 : 3; // Heart = 2, Oxygen = 3
      const callbackName =
        callbackType === 0x74 ? 'MeasurementHeartCallback' : 'MeasurementOxygenCallback';

      this.logger.debug(
        '🔍 Parsing callback measurement data (official SDK pattern):',
        'ble-manager',
        {
          callbackType: `0x${callbackType.toString(16).padStart(2, '0')}`,
          callbackName,
          measurementType: measurementType === 2 ? 'heart rate' : 'oxygen',
          fullDataLength: bytes.length,
          allBytes: Array.from(bytes.slice(0, Math.min(bytes.length, 16)))
            .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
            .join(' '),
        }
      );

      // Extract measurement value following official SDK pattern
      let measurementValue = null;

      if (measurementType === 2) {
        // Heart rate callback - look for valid BPM values
        for (let i = 1; i < Math.min(bytes.length, 10); i++) {
          if (bytes[i] > 30 && bytes[i] <= 220) {
            measurementValue = bytes[i];
            this.logger.debug(
              `Found heart rate in callback at byte[${i}]: ${measurementValue} BPM`,
              'ble-manager'
            );
            break;
          }
        }
      } else if (measurementType === 3) {
        // Oxygen callback - look for valid SpO2 values
        for (let i = 1; i < Math.min(bytes.length, 10); i++) {
          if (bytes[i] >= 80 && bytes[i] <= 100) {
            measurementValue = bytes[i];
            this.logger.debug(
              `Found oxygen in callback at byte[${i}]: ${measurementValue}%`,
              'ble-manager'
            );
            break;
          }
        }
      }

      if (measurementValue !== null) {
        this.logger.info(
          `🎯 ${measurementType === 2 ? 'Heart rate' : 'Oxygen'} measurement from official callback: ${measurementValue}${measurementType === 2 ? ' BPM' : '%'}`,
          'ble-manager'
        );

        // Return data in official SDK format
        const dataMap: any = {};
        const valueKey = measurementType === 2 ? 'heartValue' : 'oxygenValue';
        dataMap[valueKey] = measurementValue.toString();

        return {
          commandType: 0x28, // Use standard live measurement command type
          measurementType: measurementType as 2 | 3,
          isComplete: true, // Callback notifications typically indicate completion
          timestamp: new Date(),
          data: {
            value: measurementValue,
            status: callbackType,
            callbackType: callbackName,
            dataMap,
            officialSDKFormat: {
              DataType: callbackName,
              Data: dataMap,
              End: true,
            },
            source: 'official_callback',
            rawBytes: Array.from(bytes),
          },
          rawData: Array.from(bytes),
        };
      }

      return null;
    } catch (error) {
      this.logger.error('Error parsing callback measurement data:', 'ble-manager', { error });
      return null;
    }
  }

  private extractPossibleMeasurementValues(bytes: Uint8Array): {
    heartRateValues: number[];
    oxygenValues: number[];
  } {
    const heartRateValues: number[] = [];
    const oxygenValues: number[] = [];

    // Skip command byte (index 0) and potential CRC byte (last index)
    // Focus on data payload bytes (indices 1 to length-2)
    // Official SDK pattern: avoid interpreting command bytes as measurement data
    const startIndex = 1;
    const endIndex = Math.max(1, bytes.length - 1);

    for (let i = startIndex; i < endIndex; i++) {
      const value = bytes[i];

      // Check for heart rate values (30-220 BPM) - exclude common command values
      if (
        value >= 30 &&
        value <= 220 &&
        // Exclude command bytes that are often misinterpreted as measurements
        value !== 0x86 &&
        value !== 0x87 &&
        value !== 0x8c &&
        value !== 0x28 &&
        value !== 0x66 &&
        value !== 0x13 &&
        value !== 0x74 &&
        value !== 0x75
      ) {
        heartRateValues.push(value);
      }

      // Check for oxygen values (80-100%) - exclude command bytes
      if (
        value >= 80 &&
        value <= 100 &&
        value !== 0x86 &&
        value !== 0x87 &&
        value !== 0x8c &&
        value !== 0x74 &&
        value !== 0x75
      ) {
        oxygenValues.push(value);
      }
    }

    return { heartRateValues, oxygenValues };
  }

  private parseSpecificMeasurementCommand(bytes: Uint8Array): LiveMeasurementResponse | null {
    try {
      const commandType = bytes[0];

      this.logger.debug(
        '🔍 Parsing specific measurement command (official SDK pattern):',
        'ble-manager',
        {
          commandType: `0x${commandType.toString(16).padStart(2, '0')}`,
          commandName: this.getCommandName(commandType),
          dataLength: bytes.length,
          allBytes: Array.from(bytes.slice(0, Math.min(bytes.length, 16)))
            .map((b) => `0x${b.toString(16).padStart(2, '0')}`)
            .join(' '),
        }
      );

      let measurementValue: number | null = null;
      let measurementType: 2 | 3 = 2; // Default to heart rate
      let callbackName = '';

      // Handle specific command types following official SDK pattern
      switch (commandType) {
        case 0x86:
        case 0x87:
          // 0x86 commands follow pattern: 0x86 [TYPE] [STATUS] [DATA1] [DATA2] ...
          // Official SDK handles these as measurement responses
          if (bytes.length >= 5) {
            const type = bytes[1]; // 0x02 = heart rate, 0x03 = oxygen (following official constants)
            const status = bytes[2];
            const data1 = bytes[3];
            const data2 = bytes[4];

            this.logger.debug(
              '🔍 Analyzing 0x86 command structure (official SDK pattern):',
              'ble-manager',
              {
                type: `0x${type.toString(16).padStart(2, '0')}`,
                status: `0x${status.toString(16).padStart(2, '0')}`,
                data1: data1,
                data2: data2,
                typeInterpretation: type === 2 ? 'heart_rate' : type === 3 ? 'oxygen' : 'unknown',
              }
            );

            // Determine measurement type from byte[1] (official SDK pattern)
            if (type === 2) {
              measurementType = 2; // Heart rate
              callbackName = 'MeasurementHeartCallback';
              // Try different positions for heart rate value
              if (data1 >= 30 && data1 <= 220) {
                measurementValue = data1;
                this.logger.debug(
                  `Found heart rate in 0x86 data1: ${measurementValue} BPM`,
                  'ble-manager'
                );
              } else if (data2 >= 30 && data2 <= 220) {
                measurementValue = data2;
                this.logger.debug(
                  `Found heart rate in 0x86 data2: ${measurementValue} BPM`,
                  'ble-manager'
                );
              }
            } else if (type === 3) {
              measurementType = 3; // Oxygen
              callbackName = 'MeasurementOxygenCallback';
              // Try different positions for oxygen value
              if (data1 >= 80 && data1 <= 100) {
                measurementValue = data1;
                this.logger.debug(
                  `Found oxygen in 0x86 data1: ${measurementValue}%`,
                  'ble-manager'
                );
              } else if (data2 >= 80 && data2 <= 100) {
                measurementValue = data2;
                this.logger.debug(
                  `Found oxygen in 0x86 data2: ${measurementValue}%`,
                  'ble-manager'
                );
              }
            }
          }
          break;

        case 0x66:
          // Oxygen data command - official SDK constant: Oxygen_data
          if (bytes.length >= 2) {
            measurementType = 3;
            callbackName = 'MeasurementOxygenCallback';
            // Oxygen data might be in various positions
            for (let i = 1; i < Math.min(bytes.length, 10); i++) {
              if (bytes[i] >= 80 && bytes[i] <= 100) {
                measurementValue = bytes[i];
                this.logger.debug(
                  `Found oxygen in 0x66 command at byte[${i}]: ${measurementValue}%`,
                  'ble-manager'
                );
                break;
              }
            }
          }
          break;

        case 0x13:
          // Battery level - might accompany measurement data in some official SDK scenarios
          if (bytes.length > 2) {
            for (let i = 2; i < Math.min(bytes.length, 8); i++) {
              const value = bytes[i];
              if (value >= 30 && value <= 220) {
                measurementValue = value;
                measurementType = 2;
                callbackName = 'MeasurementHeartCallback';
                this.logger.debug(
                  `Found heart rate with battery data at byte[${i}]: ${value} BPM`,
                  'ble-manager'
                );
                break;
              }
              if (value >= 80 && value <= 100) {
                measurementValue = value;
                measurementType = 3;
                callbackName = 'MeasurementOxygenCallback';
                this.logger.debug(
                  `Found oxygen with battery data at byte[${i}]: ${value}%`,
                  'ble-manager'
                );
                break;
              }
            }
          }
          break;
      }

      if (measurementValue !== null) {
        this.logger.info(
          `🎯 Successfully extracted ${measurementType === 2 ? 'heart rate' : 'oxygen'} from command 0x${commandType.toString(16).padStart(2, '0')} (official SDK pattern): ${measurementValue}${measurementType === 2 ? ' BPM' : '%'}`,
          'ble-manager'
        );

        // Return data in official SDK format
        const dataMap: any = {};
        const valueKey = measurementType === 2 ? 'heartValue' : 'oxygenValue';
        dataMap[valueKey] = measurementValue.toString();

        return {
          commandType: 0x28, // Use standard live measurement command type
          measurementType,
          isComplete: true, // Assume specific commands indicate completion
          timestamp: new Date(),
          data: {
            value: measurementValue,
            status: commandType,
            callbackType: callbackName,
            dataMap,
            officialSDKFormat: {
              DataType: callbackName,
              Data: dataMap,
              End: true,
            },
            source: 'specific_command',
            originalCommand: commandType,
            rawBytes: Array.from(bytes),
          },
          rawData: Array.from(bytes),
        };
      }

      return null;
    } catch (error) {
      this.logger.error('Error parsing specific measurement command:', 'ble-manager', { error });
      return null;
    }
  }

  private tryExtractMeasurementFromAnyResponse(bytes: Uint8Array): LiveMeasurementResponse | null {
    try {
      const possibleValues = this.extractPossibleMeasurementValues(bytes);

      // If we find any potential measurement values, create a response
      if (possibleValues.heartRateValues.length > 0 || possibleValues.oxygenValues.length > 0) {
        let measurementValue: number | null = null;
        let measurementType: 2 | 3 = 2; // Default to heart rate

        // Prefer heart rate values if found
        if (possibleValues.heartRateValues.length > 0) {
          measurementValue = possibleValues.heartRateValues[0]; // Take first valid value
          measurementType = 2;
        } else if (possibleValues.oxygenValues.length > 0) {
          measurementValue = possibleValues.oxygenValues[0]; // Take first valid value
          measurementType = 3;
        }

        if (measurementValue !== null) {
          this.logger.info(
            `🔍 Extracted ${measurementType === 2 ? 'heart rate' : 'oxygen'} from unknown notification: ${measurementValue}${measurementType === 2 ? ' BPM' : '%'}`,
            'ble-manager',
            {
              commandType: `0x${bytes[0].toString(16).padStart(2, '0')}`,
              extractedValue: measurementValue,
              allPossibleHeartRates: possibleValues.heartRateValues,
              allPossibleOxygen: possibleValues.oxygenValues,
            }
          );

          return {
            commandType: 0x28, // Use standard live measurement command type
            measurementType,
            isComplete: true, // Assume any extracted value indicates completion
            timestamp: new Date(),
            data: {
              value: measurementValue,
              status: bytes[0], // Use first byte as status
              source: 'extracted',
              rawBytes: Array.from(bytes),
            },
            rawData: Array.from(bytes),
          };
        }
      }

      return null;
    } catch (error) {
      this.logger.error('Error extracting measurement from unknown response:', 'ble-manager', {
        error,
      });
      return null;
    }
  }

  private getCommandName(commandType: number): string {
    const commandNames: { [key: number]: string } = {
      0x13: 'Battery Level',
      0x28: 'Live Measurement',
      0x66: 'Oxygen Data',
      0x74: 'Heart Rate Callback',
      0x75: 'Oxygen Callback',
      0x86: 'Unknown Measurement Response',
      0x87: 'Unknown Measurement Response',
    };
    return commandNames[commandType] || 'Unknown Command';
  }

  // Connection status for debugging
  getConnectionStatus(): { isConnected: boolean; deviceId: string | null; isConnecting: boolean } {
    return {
      isConnected: !!this.connectedDevice,
      deviceId: this.connectedDeviceId,
      isConnecting: this.isConnecting,
    };
  }

  destroy(): void {
    this.bleManager.stopDeviceScan();
    this.heartRateCallbacks.clear();
    this.logger.info('J Style BLE manager destroyed', 'ble-manager');
  }
}
