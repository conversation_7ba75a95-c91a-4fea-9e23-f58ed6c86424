import { useQuery } from '@tanstack/react-query';
import { useDependencyInjection } from '../../../core/hooks/use-dependency-injection';
import { PATIENT_SYMBOLS } from '../../business/types/patient-symbols';
import { patientContainer } from '../../integration/register-dependency-injection';
import { IGetPatients } from '../../business/api/patient.service';
import { PatientListParams } from '../../business/types/patient-list-params';

export const useGetPatients = (params: PatientListParams) => {
  const getPatients: IGetPatients = useDependencyInjection(
    patientContainer,
    PATIENT_SYMBOLS.GetPatients
  );
  return useQuery({
    queryKey: ['patients', params],
    queryFn: () => getPatients.execute(params),
  });
};
