import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { COLORS } from '../../business/constants/colors';

interface DrawerNavigationProps {
  navigation: any;
}

interface NavigationItem {
  label: string;
  route: string;
  iconName: keyof typeof Ionicons.glyphMap;
}

const navigationItems: NavigationItem[] = [
  {
    label: 'Dashboard',
    route: '/dashboard',
    iconName: 'grid-outline',
  },
  {
    label: 'Facilities',
    route: '/(app)/facilities',
    iconName: 'business-outline',
  },
  {
    label: 'Device ',
    route: '/(app)/reporting',
    iconName: 'bluetooth-outline',
  },
];

export default function DrawerNavigation({ navigation }: DrawerNavigationProps) {
  const handleNavigation = (route: string) => {
    router.replace(route as any);
    navigation.closeDrawer();
  };

  return (
    <ScrollView contentContainerStyle={styles.drawerContent}>
      {navigationItems.map((item) => (
        <TouchableOpacity
          key={item.route}
          style={styles.drawerItem}
          onPress={() => handleNavigation(item.route)}
        >
          <View style={styles.drawerItemContent}>
            <Ionicons name={item.iconName} size={24} color={COLORS.ACCENT} style={styles.icon} />
            <Text style={styles.drawerItemLabel}>{item.label}</Text>
          </View>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  drawerContent: {
    flex: 1,
    paddingTop: 20,
  },
  drawerItem: {
    marginHorizontal: 16,
    marginVertical: 2,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  drawerItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 32,
  },
  drawerItemLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: COLORS.TEXT_MAIN,
    fontFamily: 'DMSans-Regular',
  },
});
