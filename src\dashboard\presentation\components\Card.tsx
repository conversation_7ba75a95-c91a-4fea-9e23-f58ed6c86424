import { PropsWithChildren } from 'react';
import { StyleSheet, Text, View } from 'react-native';

interface Props extends PropsWithChildren {
  title: string;
}
export const Card = ({ children, title }: Props) => {
  return (
    <View style={styles.card}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.content}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF80',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2B3674',
    marginBottom: 16,
    fontFamily: 'DMSans-SemiBold',
  },
  content: {
    gap: 12,
  },
});
