import React, { ComponentType, ReactNode, Suspense } from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import LazyLoadErrorBoundary from './LazyLoadErrorBoundary';

interface LazyWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
  loadingText?: string;
}

// Default fallback component
const DefaultFallback = ({ loadingText = 'Loading...' }: { loadingText?: string }) => (
  <View style={styles.defaultFallback}>
    <ActivityIndicator size="large" color="#4A90E2" />
    <Text style={styles.loadingText}>{loadingText}</Text>
  </View>
);

// Reusable lazy wrapper component
export const LazyWrapper: React.FC<LazyWrapperProps> = ({
  children,
  fallback,
  errorFallback,
  loadingText,
}) => {
  const suspenseFallback = fallback || <DefaultFallback loadingText={loadingText} />;

  return (
    <LazyLoadErrorBoundary fallback={errorFallback}>
      <Suspense fallback={suspenseFallback}>{children}</Suspense>
    </LazyLoadErrorBoundary>
  );
};

// Helper function to create lazy components with wrapper
export const createLazyComponent = <P extends object>(
  lazyComponent: () => Promise<{ default: ComponentType<P> }>,
  fallback?: ReactNode,
  loadingText?: string
) => {
  const LazyComponent = React.lazy(lazyComponent);

  return (props: P) => (
    <LazyWrapper fallback={fallback} loadingText={loadingText}>
      <LazyComponent {...props} />
    </LazyWrapper>
  );
};

const styles = StyleSheet.create({
  defaultFallback: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    minHeight: 200,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
});

export default LazyWrapper;
