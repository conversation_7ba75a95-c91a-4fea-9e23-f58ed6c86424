import { COLORS } from '@/src/shared/business/constants/colors';
import { LazyBluetoothRadar } from '@/src/shared/presentation/components/LazyBluetoothRadar';
import { useBLE } from '@/src/shared/presentation/hooks/use-ble';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { Device } from 'react-native-ble-plx';
import Animated, {
  FadeIn,
  FadeInDown,
  FadeInUp,
  FadeOut,
  FadeOutUp,
  LinearTransition,
  SlideInDown,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

interface DeviceItemProps {
  deviceName: string;
  onPress: () => void;
  isSelected?: boolean;
  index: number;
  isConnecting?: boolean;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

const DeviceItem = ({
  deviceName,
  onPress,
  isSelected = false,
  index,
  isConnecting = false,
}: DeviceItemProps) => {
  const scale = useSharedValue(1);
  const backgroundColor = useSharedValue(isSelected ? '#E3F2FD' : 'transparent');

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    backgroundColor: backgroundColor.value,
  }));

  const handlePressIn = () => {
    if (!isConnecting) {
      scale.value = withSpring(0.95);
    }
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  useEffect(() => {
    backgroundColor.value = withTiming(isSelected ? '#E3F2FD' : 'transparent', {
      duration: 300,
    });
  }, [isSelected]);

  return (
    <AnimatedTouchableOpacity
      entering={FadeInDown.delay(index * 100).springify()}
      layout={LinearTransition.springify()}
      style={[styles.deviceItem, animatedStyle]}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={isConnecting}
    >
      <Ionicons name="bluetooth" size={24} color={isSelected ? '#007AFF' : '#666'} />
      <Text style={[styles.deviceName, { color: isSelected ? '#007AFF' : '#333' }]}>
        {deviceName}
      </Text>
      {isConnecting && (
        <Animated.View entering={FadeIn.duration(300)}>
          <Ionicons name="ellipse-outline" size={20} color="#007AFF" />
        </Animated.View>
      )}
      {isSelected && !isConnecting && (
        <Animated.View entering={FadeIn.duration(300)}>
          <Ionicons name="checkmark-circle" size={20} color="#007AFF" />
        </Animated.View>
      )}
    </AnimatedTouchableOpacity>
  );
};

export const ListAvailableDevices = ({
  devices,
  onDevicePress,
  connectingDeviceId,
}: {
  devices: Device[];
  onDevicePress: (device: Device) => void;
  connectingDeviceId: string | null;
}) => {
  if (devices.length === 0) {
    return null;
  }

  return (
    <Animated.View
      entering={FadeInUp.duration(400)}
      exiting={FadeOut.duration(300)}
      style={styles.devicesContainer}
    >
      <FlatList
        data={devices}
        renderItem={({ item, index }) => (
          <DeviceItem
            deviceName={item.name || item.localName || 'Unknown device'}
            isSelected={false}
            index={index}
            isConnecting={connectingDeviceId === item.id}
            onPress={() => onDevicePress(item)}
          />
        )}
        keyExtractor={(item) => item.id}
        style={styles.devicesList}
      />
    </Animated.View>
  );
};

const PermissionDeniedView = ({ onRequestPermissions }: { onRequestPermissions: () => void }) => (
  <Animated.View
    entering={FadeInDown.duration(500)}
    exiting={FadeOutUp.duration(300)}
    style={styles.permissionContainer}
  >
    <Animated.View entering={FadeIn.delay(200)}>
      <Ionicons name="bluetooth-outline" size={48} color="#666" />
    </Animated.View>
    <Animated.Text entering={FadeInDown.delay(300)} style={styles.permissionTitle}>
      Bluetooth permissions required
    </Animated.Text>
    <Animated.Text entering={FadeInDown.delay(400)} style={styles.permissionText}>
      To connect devices you need to grant Bluetooth permissions
    </Animated.Text>
    <AnimatedTouchableOpacity
      entering={SlideInDown.delay(500).springify()}
      style={styles.permissionButton}
      onPress={onRequestPermissions}
    >
      <Text style={styles.permissionButtonText}>Request permissions</Text>
    </AnimatedTouchableOpacity>
  </Animated.View>
);

const NoDevicesFoundView = ({ onRetry }: { onRetry: () => void }) => (
  <Animated.View
    entering={FadeInDown.duration(500)}
    exiting={FadeOut.duration(300)}
    style={styles.noDevicesFoundContainer}
  >
    <Animated.View entering={FadeIn.delay(200)}>
      <Ionicons name="bluetooth-outline" size={48} color="#999" />
    </Animated.View>
    <Animated.Text entering={FadeInDown.delay(300)} style={styles.noDevicesFoundTitle}>
      No devices found
    </Animated.Text>
    <Animated.Text entering={FadeInDown.delay(400)} style={styles.noDevicesFoundText}>
      Make sure your device is turned on, nearby and in pairing mode
    </Animated.Text>
    <AnimatedTouchableOpacity
      entering={SlideInDown.delay(500).springify()}
      style={styles.retryButton}
      onPress={onRetry}
    >
      <Text style={styles.retryButtonText}>Scan again</Text>
    </AnimatedTouchableOpacity>
  </Animated.View>
);

const ConnectionSuccessView = ({ deviceName }: { deviceName: string }) => {
  const buttonScale = useSharedValue(1);

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
  }));

  const handleDonePress = () => {
    buttonScale.value = withSpring(0.95, {}, () => {
      buttonScale.value = withSpring(1);
    });
    // Navigate back or to previous screen
    router.back();
  };

  return (
    <Animated.View
      entering={FadeInDown.duration(500)}
      exiting={FadeOut.duration(300)}
      style={styles.successContainer}
    >
      <Animated.View entering={FadeIn.delay(200)} style={styles.successContent}>
        <Animated.View entering={FadeInDown.delay(300)}>
          <Ionicons name="checkmark-circle" size={64} color="#34C759" />
        </Animated.View>

        <Animated.Text entering={FadeInDown.delay(400)} style={styles.successText}>
          {deviceName} is now connected
        </Animated.Text>

        <AnimatedTouchableOpacity
          entering={SlideInDown.delay(500).springify()}
          style={[styles.doneButton, buttonAnimatedStyle]}
          onPress={handleDonePress}
        >
          <Text style={styles.connectButtonText}>Done</Text>
        </AnimatedTouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

const ConnectingView = () => (
  <Animated.View
    entering={FadeInDown.duration(500)}
    exiting={FadeOut.duration(300)}
    style={styles.connectingContainer}
  >
    <Animated.View entering={FadeIn.delay(200)}>
      <ActivityIndicator size="large" color="#007AFF" />
    </Animated.View>
    <Animated.Text entering={FadeInDown.delay(300)} style={styles.connectingText}>
      Connecting to device...
    </Animated.Text>
  </Animated.View>
);

const ConnectionFailedView = ({
  deviceName,
  onRetry,
}: {
  deviceName: string;
  onRetry: () => void;
}) => {
  const buttonScale = useSharedValue(1);

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
  }));

  const handleRetryPress = () => {
    buttonScale.value = withSpring(0.95, {}, () => {
      buttonScale.value = withSpring(1);
    });
    onRetry();
  };

  return (
    <Animated.View
      entering={FadeInDown.duration(500)}
      exiting={FadeOut.duration(300)}
      style={styles.successContainer}
    >
      <Animated.View entering={FadeIn.delay(200)} style={styles.successContent}>
        <Animated.View entering={FadeInDown.delay(300)}>
          <Ionicons name="close-circle" size={64} color="#FF3B30" />
        </Animated.View>

        <Animated.Text entering={FadeInDown.delay(400)} style={styles.failureText}>
          Failed to connect to {deviceName}
        </Animated.Text>

        <AnimatedTouchableOpacity
          entering={SlideInDown.delay(500).springify()}
          style={[styles.retryButton, buttonAnimatedStyle]}
          onPress={handleRetryPress}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </AnimatedTouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

export default function ConnectDevice() {
  const { requestPermissions, allDevices, scanForDevices, isScanning, stopScan, connectToDevice } =
    useBLE();
  const [permissionStatus, setPermissionStatus] = useState<'pending' | 'granted' | 'denied'>(
    'pending'
  );
  const [showNoDevicesFound, setShowNoDevicesFound] = useState(false);
  const [connectingDeviceId, setConnectingDeviceId] = useState<string | null>(null);
  const [connectedDeviceName, setConnectedDeviceName] = useState<string | null>(null);
  const [failedDeviceName, setFailedDeviceName] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<
    'idle' | 'connecting' | 'connected' | 'failed'
  >('idle');

  const handleRequestPermissions = async () => {
    const granted = await requestPermissions();
    if (granted) {
      setPermissionStatus('granted');
      startScanWithTimeout();
    } else {
      setPermissionStatus('denied');
    }
  };

  const startScanWithTimeout = () => {
    setShowNoDevicesFound(false);
    setConnectionStatus('idle');
    setConnectedDeviceName(null);
    setFailedDeviceName(null);
    scanForDevices();
  };

  const handleDevicePress = async (device: Device) => {
    console.log({ device });
    setConnectingDeviceId(device.id);
    setConnectionStatus('connecting');

    try {
      await connectToDevice(device);
      // Connection successful
      setConnectedDeviceName(device.name || device.localName || 'Unknown device');
      setConnectionStatus('connected');
      stopScan(); // Stop scanning on successful connection
    } catch (error) {
      // Connection failed
      setFailedDeviceName(device.name || device.localName || 'Unknown device');
      setConnectionStatus('failed');
      console.error('Connection failed:', error);
    } finally {
      setConnectingDeviceId(null);
    }
  };

  const handleRetrySearch = () => {
    setConnectionStatus('idle');
    setConnectedDeviceName(null);
    setFailedDeviceName(null);
    startScanWithTimeout();
  };

  const handleConnectionRetry = () => {
    // Reset all states and restart the scanning process
    setConnectionStatus('idle');
    setConnectedDeviceName(null);
    setFailedDeviceName(null);
    setConnectingDeviceId(null);
    setShowNoDevicesFound(false);
    startScanWithTimeout();
  };

  useEffect(() => {
    handleRequestPermissions();
  }, []);

  const handleRetryPermissions = () => {
    setPermissionStatus('pending');
    handleRequestPermissions();
  };

  return (
    <SafeAreaView style={styles.container}>
      {permissionStatus === 'granted' && (
        <View style={styles.contentContainer}>
          {connectionStatus === 'connected' && connectedDeviceName ? (
            <ConnectionSuccessView deviceName={connectedDeviceName} />
          ) : connectionStatus === 'failed' && failedDeviceName ? (
            <ConnectionFailedView deviceName={failedDeviceName} onRetry={handleConnectionRetry} />
          ) : connectionStatus === 'connecting' ? (
            <ConnectingView />
          ) : (
            <>
              <View style={styles.mainContent}>
                {isScanning && <LazyBluetoothRadar />}
                {showNoDevicesFound && connectionStatus === 'idle' && (
                  <NoDevicesFoundView onRetry={handleRetrySearch} />
                )}

                {!showNoDevicesFound && (
                  <ListAvailableDevices
                    devices={allDevices}
                    onDevicePress={handleDevicePress}
                    connectingDeviceId={connectingDeviceId}
                  />
                )}
              </View>
            </>
          )}
        </View>
      )}

      {permissionStatus === 'denied' && (
        <PermissionDeniedView onRequestPermissions={handleRetryPermissions} />
      )}

      {permissionStatus === 'pending' && (
        <Animated.View
          entering={FadeIn.duration(400)}
          exiting={FadeOut.duration(300)}
          style={styles.loadingContainer}
        >
          <Animated.Text entering={FadeInDown.delay(200)} style={styles.loadingText}>
            Requesting permissions...
          </Animated.Text>
        </Animated.View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    gap: 32,
    marginTop: 50,
  },
  contentContainer: {
    flex: 1,
  },
  mainContent: {
    flex: 1,
  },
  scanningContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: 'center',
  },
  scanningText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  devicesContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  devicesTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: '#333',
  },
  devicesList: {
    flex: 1,
  },
  deviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 15,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  permissionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  permissionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  noDevicesFoundContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  noDevicesFoundTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  noDevicesFoundText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#34C759',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  connectingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
  },
  connectingText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    textAlign: 'center',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  successContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
  },
  successText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  connectButtonContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: 'center',
  },
  connectButtonContainerAndroid: {
    display: 'flex',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  doneButton: {
    backgroundColor: COLORS.ACCENT,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 999,
    minWidth: 120,
  },
  connectButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center',
  },
  failureText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    paddingHorizontal: 40,
  },
});
