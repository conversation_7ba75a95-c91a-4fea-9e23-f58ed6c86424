import React, { useEffect, useRef } from 'react';
import { Animated, Platform, StyleSheet, View } from 'react-native';

interface SkeletonLoaderProps {
  variant?: 'card' | 'table-row' | 'facility-table' | 'patient-card' | 'custom';
  count?: number;
  customConfig?: SkeletonShape[];
  containerStyle?: object;
  itemStyle?: object;
}

interface SkeletonShape {
  width: number | string;
  height: number;
  borderRadius?: number;
  marginBottom?: number;
  marginRight?: number;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  variant = 'card',
  count = 3,
  customConfig,
  containerStyle,
  itemStyle,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const getSkeletonConfig = (): SkeletonShape[] => {
    if (customConfig) {
      return customConfig;
    }

    switch (variant) {
      case 'facility-table':
        return [
          { width: 40, height: 20, borderRadius: 4, marginRight: 12 }, // Sr No
          { width: 120, height: 20, borderRadius: 4, marginRight: 12 }, // Name
          { width: 80, height: 16, borderRadius: 4, marginRight: 12 }, // Patients
          { width: 80, height: 16, borderRadius: 4, marginRight: 12 }, // Devices
          { width: 70, height: 16, borderRadius: 4, marginRight: 12 }, // Alerts
          { width: 150, height: 16, borderRadius: 4, marginRight: 12 }, // Address
          { width: 20, height: 20, borderRadius: 4 }, // Chevron
        ];
      case 'patient-card':
        return [
          { width: '80%', height: 22, borderRadius: 4, marginBottom: 8 }, // Patient Name
          { width: '60%', height: 16, borderRadius: 4, marginBottom: 12 }, // DOB/Age
          { width: '40%', height: 16, borderRadius: 4, marginBottom: 8 }, // Room
          { width: '70%', height: 16, borderRadius: 4, marginBottom: 12 }, // ICD
          { width: '35%', height: 28, borderRadius: 14 }, // Status badge
        ];
      case 'table-row':
        return [{ width: '100%', height: 24, borderRadius: 4 }];
      case 'card':
      default:
        return [
          { width: '70%', height: 20, borderRadius: 4, marginBottom: 8 }, // Name
          { width: '50%', height: 16, borderRadius: 4, marginBottom: 12 }, // DOB/Age
          { width: '40%', height: 16, borderRadius: 4, marginBottom: 8 }, // Room
          { width: '60%', height: 16, borderRadius: 4, marginBottom: 12 }, // ICD
          { width: '30%', height: 24, borderRadius: 12 }, // Status badge
        ];
    }
  };

  const renderSkeletonItem = (index: number) => {
    const config = getSkeletonConfig();

    if (variant === 'facility-table') {
      return (
        <View key={index} style={[styles.facilityTableRowSkeleton, itemStyle]}>
          {config.map((shape, shapeIndex) => (
            <Animated.View
              key={shapeIndex}
              style={[
                styles.skeletonShape,
                {
                  width: shape.width as any,
                  height: shape.height,
                  borderRadius: shape.borderRadius || 0,
                  marginBottom: shape.marginBottom || 0,
                  marginRight: shape.marginRight || 0,
                  opacity,
                },
              ]}
            />
          ))}
        </View>
      );
    }

    if (variant === 'table-row') {
      return (
        <View key={index} style={[styles.tableRowSkeleton, itemStyle]}>
          {config.map((shape, shapeIndex) => (
            <Animated.View
              key={shapeIndex}
              style={[
                styles.skeletonShape,
                {
                  width: shape.width as any,
                  height: shape.height,
                  borderRadius: shape.borderRadius || 0,
                  marginBottom: shape.marginBottom || 0,
                  marginRight: shape.marginRight || 0,
                  opacity,
                },
              ]}
            />
          ))}
        </View>
      );
    }

    return (
      <View key={index} style={[styles.cardSkeleton, itemStyle]}>
        {config.map((shape, shapeIndex) => (
          <Animated.View
            key={shapeIndex}
            style={[
              styles.skeletonShape,
              {
                width: shape.width as any,
                height: shape.height,
                borderRadius: shape.borderRadius || 0,
                marginBottom: shape.marginBottom || 0,
                marginRight: shape.marginRight || 0,
                opacity,
              },
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {Array.from({ length: count }, (_, index) => renderSkeletonItem(index))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
  },
  cardSkeleton: {
    backgroundColor: '#FFFFFF80',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        // elevation: 1,
      },
    }),
  },
  tableRowSkeleton: {
    backgroundColor: '#FFFFFF80',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  facilityTableRowSkeleton: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    minHeight: 60,
  },
  skeletonShape: {
    backgroundColor: '#e5e7eb',
  },
});

export default SkeletonLoader;
