import { Facility } from '@/src/facilities/business/types/facility-response';
import { SCREEN_SIZE } from '@/src/shared/business/constants/screen-size';
import { Ionicons } from '@expo/vector-icons';
import React, { memo } from 'react';
import { Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useFacilityInfo } from '../hooks/use-facility-formatted-info';

interface Props {
  facility: Facility;
  handleRowPress: (facility: Facility) => void;
}
const screenWidth = Dimensions.get('window').width;
const isSmallScreen = screenWidth < SCREEN_SIZE.SMALL;

const FacilityTableRowComponent = ({ facility, handleRowPress }: Props) => {
  const { srNo, name, numberOrPatients, numberOfDevices, numberOfAlerts, address } =
    useFacilityInfo(facility);

  return (
    <TouchableOpacity style={styles.row} onPress={() => handleRowPress(facility)}>
      <Text
        style={[
          styles.cell,
          styles.srNoColumn,

          isSmallScreen && { textAlign: 'center', fontSize: 18 },
        ]}
      >
        {srNo}
      </Text>
      <View style={[styles.cell, styles.nameColumn]}>
        <Text numberOfLines={1} ellipsizeMode="tail" style={styles.facilityName}>
          {name}
        </Text>
        {isSmallScreen && (
          <View style={styles.mobileDetails}>
            <View>
              <Text numberOfLines={1} ellipsizeMode="tail" style={styles.addressText}>
                {address || 'Address can write here.'}
              </Text>
            </View>
            <View style={styles.mobileDetailText}>
              <View style={styles.mobileDetailItem}>
                <Ionicons name="person-outline" size={18} />
                <Text style={{ fontSize: 14 }}> {numberOrPatients}</Text>
              </View>
              <View style={styles.mobileDetailItem}>
                <Ionicons name="phone-portrait-outline" size={18} />
                <Text style={{ fontSize: 14 }}>{numberOfDevices}</Text>
              </View>
              <View style={styles.mobileDetailItem}>
                <Ionicons name="alert-circle-outline" size={18} />
                <Text style={{ fontSize: 14 }}> {numberOfAlerts}</Text>
              </View>
            </View>
          </View>
        )}
      </View>
      {!isSmallScreen && (
        <>
          <Text style={[styles.cell, styles.numberColumn]}>{numberOrPatients}</Text>
          <Text style={[styles.cell, styles.numberColumn]}>{numberOfDevices}</Text>
          <Text style={[styles.cell, styles.numberColumn]}>{numberOfAlerts}</Text>
          <View style={[styles.cell, styles.addressColumn]}>
            <Text style={styles.addressText}>{address || 'Address can write here.'}</Text>
          </View>
        </>
      )}

      <View style={[styles.cell, styles.chevronColumn]}>
        <Ionicons name="chevron-forward" size={20} color="#3366FF" />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cell: {
    paddingHorizontal: 8,
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
    minHeight: 60,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  facilityName: {
    fontSize: 10,
    fontWeight: '500',
    color: '#212529',
    marginBottom: 2,
  },
  mobileDetails: {
    marginTop: 4,
    display: 'flex',
    gap: 8,
  },
  mobileDetailText: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    gap: 6,
  },
  mobileDetailItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  addressText: {
    fontSize: 14,
    color: '#495057',
    lineHeight: 20,
  },
  addressColumn: {
    flex: 1.5,
    minWidth: 120,
  },
  srNoColumn: {
    width: 60,
    textAlign: 'center',
    fontSize: 10,
  },
  nameColumn: {
    flex: 2,
    width: 100,
  },
  numberColumn: {
    width: 100,
    textAlign: 'center',
    fontSize: 10,
  },
  chevronColumn: {
    width: 40,
    alignItems: 'center',
  },
});

export const FacilityTableRow = memo(FacilityTableRowComponent);
