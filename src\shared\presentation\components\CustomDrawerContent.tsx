import { router } from 'expo-router';
import React from 'react';
import { Alert, ImageBackground, StyleSheet, View } from 'react-native';

import { useSession } from '@/src/authentication/presentation/context/AuthContext';
import DrawerNavigation from './DrawerNavigation';
import HeaderLogo from './HeaderLogo';
import LogoutSection from './LogoutSection';

interface CustomDrawerContentProps {
  navigation: any;
}

export default function CustomDrawerContent({ navigation }: CustomDrawerContentProps) {
  const { signOut } = useSession();
  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            navigation.closeDrawer();
            signOut();
            router.replace('/(auth)/login');
          },
        },
      ],
      { cancelable: true }
    );
  };

  return (
    <ImageBackground
      source={require('../../../../assets/images/background.png')}
      style={styles.drawerContainer}
      resizeMode="cover"
    >
      <View style={styles.overlay}>
        <HeaderLogo />

        {/* Navigation Items */}
        <DrawerNavigation navigation={navigation} />

        {/* Logout Button */}
        <LogoutSection onLogout={handleLogout} />
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});
