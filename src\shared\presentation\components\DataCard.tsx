import { Ionicons } from '@expo/vector-icons';
import React, { memo, useCallback, useMemo } from 'react';
import { Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export interface DataCardField<T> {
  key: keyof T | string;
  label?: string;
  render?: (item: T, value: any) => React.ReactNode;
  style?: 'default' | 'header' | 'subtitle' | 'body' | 'caption';
  hideLabel?: boolean;
}

export interface DataCardSection<T> {
  type: 'header' | 'info' | 'footer';
  fields: DataCardField<T>[];
  style?: object;
}

interface DataCardProps<T> {
  data: T;
  sections: DataCardSection<T>[];
  onPress?: (item: T) => void;
  showChevron?: boolean;
  cardStyle?: object;
  contentStyle?: object;
}

function DataCard<T>({
  data,
  sections,
  onPress,
  showChevron = false,
  cardStyle = {},
  contentStyle = {},
}: DataCardProps<T>) {
  // Memoize the onPress handler to prevent unnecessary re-renders
  const handlePress = useCallback(() => {
    onPress?.(data);
  }, [onPress, data]);

  // Memoize combined styles to prevent object recreation
  const combinedCardStyle = useMemo(() => [styles.cardContainer, cardStyle], [cardStyle]);

  const combinedContentStyle = useMemo(() => [styles.cardContent, contentStyle], [contentStyle]);

  // Memoize render functions to prevent recreation on every render
  const renderField = useCallback((field: DataCardField<T>, item: T) => {
    const value = field.key === 'custom' ? null : (item as any)[field.key];
    const content = field.render ? field.render(item, value) : String(value || '');

    if (field.hideLabel || field.style === 'header' || field.style === 'subtitle') {
      return (
        <View key={String(field.key)} style={styles.fieldContainer}>
          {typeof content === 'string' ? (
            <Text
              style={[
                styles.fieldText,
                field.style === 'header' && styles.headerText,
                field.style === 'subtitle' && styles.subtitleText,
                field.style === 'body' && styles.bodyText,
                field.style === 'caption' && styles.captionText,
              ]}
              numberOfLines={field.style === 'header' ? 1 : undefined}
            >
              {content}
            </Text>
          ) : (
            content
          )}
        </View>
      );
    }

    return (
      <View key={String(field.key)} style={styles.labelValueContainer}>
        {field.label && <Text style={styles.labelText}>{field.label}</Text>}
        {typeof content === 'string' ? (
          <Text
            style={[
              styles.valueText,
              field.style === 'body' && styles.bodyText,
              field.style === 'caption' && styles.captionText,
            ]}
            numberOfLines={1}
          >
            {content}
          </Text>
        ) : (
          content
        )}
      </View>
    );
  }, []);

  const renderSection = useCallback(
    (section: DataCardSection<T>) => {
      const sectionStyle =
        section.type === 'header'
          ? styles.headerSection
          : section.type === 'footer'
            ? styles.footerSection
            : styles.infoSection;

      return (
        <View key={section.type} style={[sectionStyle, section.style]}>
          {section.type === 'header' && showChevron ? (
            <View style={styles.headerWithChevron}>
              <View style={styles.headerContent}>
                {section.fields.map((field) => renderField(field, data))}
              </View>
              <Ionicons name="chevron-forward" size={20} color="#666666" />
            </View>
          ) : section.type === 'footer' ? (
            <View style={styles.footerContent}>
              {section.fields.map((field) => renderField(field, data))}
            </View>
          ) : (
            section.fields.map((field) => renderField(field, data))
          )}
        </View>
      );
    },
    [data, showChevron, renderField]
  );

  return (
    <TouchableOpacity
      style={combinedCardStyle}
      onPress={handlePress}
      activeOpacity={onPress ? 0.7 : 1}
      disabled={!onPress}
    >
      <View style={combinedContentStyle}>{sections.map((section) => renderSection(section))}</View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: '#FFFFFF80',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 6,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
      },
      android: {
        // elevation: 2,
        borderWidth: 0.5,
        borderColor: '#E5E7EB',
      },
    }),
  },
  cardContent: {
    padding: 16,
  },
  headerSection: {
    marginBottom: 8,
  },
  infoSection: {
    marginBottom: 6,
  },
  footerSection: {
    marginTop: 8,
  },
  headerWithChevron: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerContent: {
    flex: 1,
    marginRight: 8,
  },
  footerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  fieldContainer: {
    marginBottom: 2,
  },
  labelValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  fieldText: {
    fontSize: 14,
    color: '#1F2937',
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  subtitleText: {
    fontSize: 14,
    color: '#6B7280',
  },
  bodyText: {
    fontSize: 14,
    color: '#374151',
  },
  captionText: {
    fontSize: 12,
    color: '#6B7280',
  },
  labelText: {
    fontSize: 14,
    color: '#374151',
    marginRight: 4,
  },
  valueText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    flex: 1,
  },
});

// Add deep comparison for memo to prevent unnecessary re-renders
const areEqual = <T,>(prevProps: DataCardProps<T>, nextProps: DataCardProps<T>): boolean => {
  // Compare primitive props
  if (prevProps.showChevron !== nextProps.showChevron || prevProps.onPress !== nextProps.onPress) {
    return false;
  }

  // Deep compare data object (shallow comparison should be sufficient for most cases)
  if (prevProps.data !== nextProps.data) {
    return false;
  }

  // Compare sections array length
  if (prevProps.sections.length !== nextProps.sections.length) {
    return false;
  }

  // Compare sections reference (assuming sections are stable)
  for (let i = 0; i < prevProps.sections.length; i++) {
    if (prevProps.sections[i] !== nextProps.sections[i]) {
      return false;
    }
  }

  return true;
};

export default memo(DataCard, areEqual) as <T>(props: DataCardProps<T>) => React.ReactElement;
