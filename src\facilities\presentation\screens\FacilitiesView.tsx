import React, { useState } from 'react';
import { RefreshControl, SafeAreaView, ScrollView, StyleSheet, Text, View } from 'react-native';

import { SearchForm } from '../../../shared/presentation/components/SearchForm';
import FacilityList from '../components/FacilityList';
import { useFacilityList } from '../hooks/use-facility-list';

const FacilitiesView = () => {
  const [search, setSearch] = useState('');
  const { data, error, refetch, isLoading, isRefetching } = useFacilityList(search);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={<RefreshControl refreshing={isRefetching} onRefresh={refetch} />}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.content}>
          {error && <Text>Error: {error.message}</Text>}
          <View style={styles.searchContainer}>
            <SearchForm
              containerStyle={{ maxWidth: 500, width: '100%' }}
              searchContainerStyle={{ width: '100%' }}
              inputStyle={{ width: '100%' }}
              placeholder="Search by facility name"
              onSearch={(value) => setSearch(value)}
            />
          </View>
          <FacilityList facilities={data?.data || []} isLoading={isLoading} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  searchContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    paddingBottom: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default FacilitiesView;
