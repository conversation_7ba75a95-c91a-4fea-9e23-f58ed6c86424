import DataTable, { DataTableColumn } from '@/src/shared/presentation/components/DataTable';
import React from 'react';
import { StyleSheet, View } from 'react-native';

export const RecordsTable = () => {
  interface RecordDevice {
    id: number;
    date: string;
    value: number;
    unit: string;
    device: string;
  }
  const columns: DataTableColumn<RecordDevice>[] = [
    {
      header: 'Date',
      key: 'date',
      width: 40,
    },
    {
      header: 'Value',
      key: 'value',
      width: 30,
    },
    {
      header: 'Device',
      key: 'device',
      width: 30,
    },
  ];

  const data = [
    {
      id: 1,
      date: '2021-01-01',
      value: 100,
      unit: 'bpm',
      device: 'Device 1',
    },
    {
      id: 2,
      date: '2021-01-02',
      value: 101,
      unit: 'bpm',
      device: 'Device 2',
    },
    {
      id: 3,
      date: '2021-01-03',
      value: 102,
      unit: 'bpm',
      device: 'Device 3',
    },
  ];
  return (
    <View style={styles.container}>
      <DataTable data={data} columns={columns} keyExtractor={(item) => item.id.toString()} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    minHeight: 170,
  },
});
