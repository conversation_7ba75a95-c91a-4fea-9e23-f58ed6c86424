import React, { useCallback, useState } from 'react';
import { SafeAreaView, StyleSheet, View } from 'react-native';

import { useLocalSearchParams, useRouter } from 'expo-router';
import PatientList from '../components/PatientList';
import PatientSearchHeader from '../components/PatientSearchHeader';
import {
  getAllPatientsFromPages,
  useGetPatientsInfinite,
} from '../hooks/use-get-patients-infinite';

interface PatientViewProps {
  facilityId?: string;
  facilityName?: string;
}

const PatientView: React.FC<PatientViewProps> = ({ facilityId }) => {
  const [search, setSearch] = useState('');
  const router = useRouter();
  const { facilityName } = useLocalSearchParams<{
    facilityName?: string;
  }>();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, refetch, isRefetching } =
    useGetPatientsInfinite({
      facilityId,
      search,
      limit: 20,
    });

  const patients = getAllPatientsFromPages(data?.pages);

  const handlePatientPress = useCallback((patient: any) => {
    router.navigate(
      `/(app)/single-patient?patientId=${patient.id}&patientName=${patient.name}&facilityId=${facilityId}&facilityName=${facilityName}`
    );
  }, []);

  const handleFilter = useCallback(() => {
    console.log('Filter pressed');
    // Handle filter logic
  }, []);

  const handleSearch = useCallback((value: string) => {
    setSearch(value);
  }, []);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <PatientSearchHeader onSearch={handleSearch} onFilter={handleFilter} />
        <View style={styles.patientListContainer}>
          <PatientList
            patients={patients}
            onPatientPress={handlePatientPress}
            onEndReached={handleLoadMore}
            isLoading={isLoading}
            isLoadingMore={isFetchingNextPage}
            onRefresh={refetch}
            refreshing={isRefetching}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  content: {
    flex: 1,
    // padding: 16,
  },
  patientListContainer: {
    paddingHorizontal: 16,
    height: 500,
  },
});

export default PatientView;
