import { COLORS } from '@/src/shared/business/constants/colors';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useResetPassword } from '../hooks/use-reset-password';

interface ResetPassswordFormData {
  email: string;
}

export default function ResetPasswordScreen() {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPassswordFormData>({
    defaultValues: {
      email: '',
    },
  });

  const { handleResetPassword, isLoading } = useResetPassword();

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-transparent"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerClassName="flex-grow-1 justify-start p-6 pt-[40px]"
        // contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View className="items-center mb-10">
          <Image
            source={require('@/assets/images/logo.png')}
            className="w-[100px] h-[100px] mb-6"
            resizeMode="contain"
          />
        </View>

        <View>
          <View className="flex flex-col gap-3">
            <Text
              className="text-[32px] font-[600] leading-[100%]"
              style={{ fontFamily: 'DM_Sans_Regular' }}
            >
              Reset Password
            </Text>
            <Text
              className="text-[14px] font-[400] leading-[100%] text-[#8B939A]"
              style={{ fontFamily: 'DM_Sans_Regular' }}
            >
              Enter your email address and we'll send you a link to reset your password
            </Text>
          </View>

          <View className="gap-[20px] mt-8">
            <View className="gap-[8px]">
              <View
                className="flex flex-row items-center border rounded-full px-[16px] py-[4px] min-h-[56px]"
                style={{ backgroundColor: COLORS.LIGHT_GRAY, borderColor: COLORS.LIGHT_GRAY }}
              >
                <Ionicons name="mail-outline" size={20} color="#9CA3AF" className="mr-[12px]" />
                <Controller
                  control={control}
                  rules={{
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Please enter a valid email address',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      className="flex-1 text-[12px] py-[6px]"
                      style={{ color: COLORS.TEXT_MAIN, fontFamily: 'DM_Sans_Regular' }}
                      placeholder="Email"
                      placeholderTextColor="#9CA3AF"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      autoCorrect={false}
                    />
                  )}
                  name="email"
                />
              </View>
              {errors.email && (
                <Text
                  className="text-[9px] ml-[4px]"
                  style={{ color: COLORS.ERROR, fontFamily: 'DM_Sans_Regular' }}
                >
                  {errors.email.message}
                </Text>
              )}
            </View>
            <TouchableOpacity className="items-end mt-[-8px] mr-1" onPress={() => router.back()}>
              <Text
                className="text-[10px]"
                style={{ color: COLORS.SUB_TEXT, fontFamily: 'DM_Sans_Regular' }}
              >
                Login?
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                { backgroundColor: COLORS.ACCENT },
                isLoading && { backgroundColor: COLORS.SUB_TEXT },
              ]}
              className={`rounded-full py-[10px] items-center mt-[8px]`}
              onPress={handleSubmit((data) => handleResetPassword(data.email))}
              disabled={isLoading}
            >
              <Text
                className="text-[14px] font-[600]"
                style={{ color: COLORS.WHITE, fontFamily: 'DM_Sans_Regular' }}
              >
                {isLoading && (
                  <View
                    className="w-[12px] h-[12px] border rounded-full animate-spin"
                    style={{ borderTopColor: COLORS.WHITE }}
                  ></View>
                )}{' '}
                {isLoading ? 'Sending...' : 'Send'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
