import { inject, injectable } from 'inversify';
import { CORE_DEPENDENCY_INJECTION_TYPES } from '../../../core/dependency-injection/types';
import { AxiosService } from '../../../core/services/axios.service';
import { IFacilityService } from '../../business/api/facility.service';
import { FacilityResponse } from '../../business/types/facility-response';

@injectable()
export class FacilityService implements IFacilityService {
  constructor(
    @inject(CORE_DEPENDENCY_INJECTION_TYPES.AxiosService)
    private readonly axiosService: AxiosService
  ) {}

  async getFacilities(search: string): Promise<FacilityResponse> {
    try {
      const response = await this.axiosService.axios.get<FacilityResponse>('/facilities', {
        params: {
          name: search,
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('FacilityService Error:', {
        message: error.message,
        name: error.name,
        code: error.code,
        stack: error.stack,
        response: error.response
          ? {
              status: error.response.status,
              statusText: error.response.statusText,
              data: error.response.data,
            }
          : 'No response object',
        request: error.request ? 'Request object exists' : 'No request object',
        isAxiosError: error.isAxiosError,
      });
      throw error;
    }
  }
}
