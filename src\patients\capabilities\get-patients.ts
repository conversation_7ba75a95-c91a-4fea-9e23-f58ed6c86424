import { inject, injectable } from 'inversify';
import { PATIENT_SYMBOLS } from '../business/types/patient-symbols';
import { IGetPatients, IPatientService } from '../business/api/patient.service';
import {
  IPatient,
  PatientBundle,
  PatientResponse,
  TypeCoding,
  Identifier,
  Extension,
} from '@/src/patients/business/types/patient-response';
import { PatientListParams } from '../business/types/patient-list-params';

@injectable()
export class GetPatients implements IGetPatients {
  constructor(
    @inject(PATIENT_SYMBOLS.PatientService)
    private readonly patientService: IPatientService
  ) {}

  async execute(
    params: PatientListParams
  ): Promise<{ patients: IPatient[]; pagination: PatientResponse['pagination'] }> {
    const response = await this.patientService.getPatients(params);
    const patients = response.data.map((patient) => this.mapPatient(patient));
    return {
      patients,
      pagination: response.pagination,
    };
  }

  mapPatient(patient: PatientBundle): IPatient {
    const patientName =
      patient.name && patient.name[0]
        ? `${patient.name[0].given?.[0] || ''} ${patient.name[0].family || ''}`.trim()
        : `Patient ${patient.id?.slice(0, 8)}`;
    const birthdate = this.calculateBirthdate(patient.birthDate);

    const age = this.calculateAge(patient.birthDate);
    const identifiers = this.calculateIdentifiers(patient.identifier);

    return {
      id: patient.id,
      name: patientName,
      dob: birthdate,
      age: age === 0 ? 'N/A' : age,
      icd: identifiers,
      alerts: this.calculateAlerts(patient),
      status: patient.active ? 'Active' : 'Inactive',
      room: 'N/A',
    };
  }

  calculateBirthdate(birthdate: Date): string {
    if (!birthdate) return 'N/A';
    const date = new Date(birthdate);
    return date.toLocaleDateString('en-US');
  }

  calculateAge(birthdate: Date): number {
    if (!birthdate) return 0;
    const today = new Date();
    const birth = new Date(birthdate);
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1;
    }
    return age;
  }

  calculateIdentifiers(identifiers: Identifier[]): string {
    if (!identifiers) return 'N/A';
    const mrn = identifiers?.find((id: Identifier) =>
      id.type?.coding?.some((coding: TypeCoding) => coding.code === 'MR')
    );
    return mrn?.value || 'N/A';
  }

  calculateAlerts(patient: PatientBundle): number {
    const alertsUrls = ['Warning Alert Count', 'Critical Alert Count'];
    const alerts = patient.extension?.filter((ext: Extension) => alertsUrls.includes(ext.url));
    const alertValue = alerts?.map((alert: Extension) => alert.valueInteger);
    return alertValue?.reduce((acc: number, curr: number | undefined) => acc + (curr || 0), 0) || 0;
  }
}
