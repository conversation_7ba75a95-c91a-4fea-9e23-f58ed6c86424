import { Ionicons } from '@expo/vector-icons';
import React, { memo } from 'react';
import { Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SearchForm } from '../../../shared/presentation/components/SearchForm';

interface Props {
  onSearch?: (value: string) => void;
  onFilter?: () => void;
}

const isMobile = Dimensions.get('window').width < 768;

const PatientSearchHeader: React.FC<Props> = ({ onSearch, onFilter }) => {
  return (
    <View style={styles.searchContainer}>
      <SearchForm
        containerStyle={{ maxWidth: 500, width: '100%' }}
        searchContainerStyle={{ width: '100%' }}
        inputStyle={{ width: '100%' }}
        placeholder="Search here..."
        onSearch={onSearch}
      />
      {!isMobile && (
        <TouchableOpacity style={styles.filterButton} onPress={onFilter}>
          <Text style={styles.filterButtonText}>Filter</Text>
          <Ionicons name="chevron-down" size={16} color="#6c757d" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  searchContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    paddingBottom: 8,
    flexDirection: 'row',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    minWidth: 80,
    justifyContent: 'center',
    marginLeft: 12,
  },
  filterButtonText: {
    fontSize: 14,
    color: '#6c757d',
    marginRight: 4,
    fontWeight: '500',
  },
});

// Memoize the component to prevent unnecessary re-renders
export { PatientSearchHeader };
export default memo(PatientSearchHeader);
