## Commit Style Guide
We follow the [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) style for our commit messages. 

**Important**: Commit messages should not be longer than 100 characters.

### Tips for concise commit messages:
- Focus on the most important change
- Use abbreviations when appropriate (e.g., "config" instead of "configuration")
- Avoid redundant words like "a", "the", "some"
- Use present tense and imperative mood
- If multiple changes, consider separate commits

### Examples:
- `feat: add user authentication module` (37 chars)
- `fix: resolve data fetching issue` (32 chars)
- `docs: update README installation guide` (37 chars)
- `style: format code with Prettier` (32 chars)
- `refactor: improve data processing performance` (45 chars)
- `test: add unit tests for user service` (37 chars)
- `chore: update dependencies` (26 chars)

### Bad vs Good examples:
❌ `refactor: reorganize app structure by moving screens to nested routes and integrating React Query for data management` (117 chars)
✅ `refactor: move screens to nested routes and add React Query` (60 chars)

