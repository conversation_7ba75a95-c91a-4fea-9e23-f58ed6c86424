import { useBLE } from '@/src/shared/presentation/hooks/use-ble';
import { useDeviceStore } from '@/src/shared/presentation/store/devivce-store';
import { router } from 'expo-router';
import { useLocalSearchParams } from 'expo-router/build/hooks';
import { Alert, StyleSheet, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MetricCard } from '../components/MetricCard';
import { PatientHeader } from '../components/PatientHeader';

const SinglePatientView = () => {
  const { top } = useSafeAreaInsets();
  const { activeDevice } = useDeviceStore();
  const { startMeasurement } = useBLE();
  // const { onnect } = useBLE();
  const { patientId, patientName } = useLocalSearchParams<{
    patientId: string;
    patientName: string;
  }>();
  const handleConnectDevice = () => {
    router.navigate({
      pathname: '/connect-device',
      params: {
        patientId,
        patientName,
      },
    });
  };

  const handleHeartBeatSync = () => {
    if (!activeDevice) {
      Alert.alert('Device not connected');
    }
    startMeasurement('heart_rate');
    console.log('heart beat sync');
  };
  const handleSPO2Sync = () => {
    if (!activeDevice) {
      Alert.alert('Device not connected');
    }
    startMeasurement('spo2');
    console.log('spo2 sync');
  };

  return (
    <View style={[styles.container, { paddingTop: top }]}>
      <PatientHeader patientName={patientName} handleConnectDevice={handleConnectDevice} />
      <View style={styles.content}>
        {/* Heart Rate Card */}
        <View style={{ width: '50%' }}>
          <MetricCard
            unit="bpm"
            value={99}
            max={100}
            min={98}
            status="normal"
            metric="Heart Rate"
            onSync={handleHeartBeatSync}
          />
        </View>
        <View style={{ width: '50%' }}>
          <MetricCard
            unit="%"
            value={99}
            max={100}
            min={98}
            status="normal"
            metric={
              <Text style={{ fontSize: 12 }}>
                SpO<Text style={{ fontSize: 9 }}>2</Text>
              </Text>
            }
            onSync={handleSPO2Sync}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
  content: {
    padding: 16,
    display: 'flex',
    width: '100%',
    flexDirection: 'row',
    gap: 8,
  },
});

export default SinglePatientView;
