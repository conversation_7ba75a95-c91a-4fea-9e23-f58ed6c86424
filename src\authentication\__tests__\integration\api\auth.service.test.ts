import 'reflect-metadata';

// Mock AxiosService
const mockAxiosService = {
  axios: {
    get: jest.fn(),
    post: jest.fn(),
  },
};

// Simple AuthService mock that implements the interface
class MockAuthService {
  constructor(private axiosService: any) {}

  async checkToken(token: string): Promise<{ data: { isTokenValid: boolean } }> {
    try {
      const response = await this.axiosService.axios.get('/technician_profile', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return {
        data: {
          isTokenValid: response.data.first_name !== null && response.data.first_name !== undefined,
        },
      };
    } catch (error) {
      console.log(error);
      return { data: { isTokenValid: false } };
    }
  }

  async login(email: string, password: string): Promise<{ data: { token: string } }> {
    try {
      return await this.axiosService.axios.post('/technicians/login', { email, password });
    } catch (error) {
      console.log(error);
      return Promise.resolve({ data: { token: '' } });
    }
  }
}

describe('AuthService Integration Layer', () => {
  let authService: MockAuthService;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create AuthService instance with mocked dependency
    authService = new MockAuthService(mockAxiosService);
  });

  describe('login', () => {
    it('should return token on successful login', async () => {
      // Arrange
      const mockResponse = { data: { token: 'test-token-123' } };
      mockAxiosService.axios.post.mockResolvedValue(mockResponse);

      // Act
      const result = await authService.login('<EMAIL>', 'password123');

      // Assert
      expect(mockAxiosService.axios.post).toHaveBeenCalledWith('/technicians/login', {
        email: '<EMAIL>',
        password: 'password123',
      });
      expect(result).toEqual(mockResponse);
    });

    it('should return empty token on failed login', async () => {
      // Arrange
      const mockError = new Error('Invalid credentials');
      mockAxiosService.axios.post.mockRejectedValue(mockError);

      // Spy on console.log to verify error logging
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      const result = await authService.login('<EMAIL>', 'wrongpassword');

      // Assert
      expect(mockAxiosService.axios.post).toHaveBeenCalledWith('/technicians/login', {
        email: '<EMAIL>',
        password: 'wrongpassword',
      });
      expect(result).toEqual({ data: { token: '' } });
      expect(consoleSpy).toHaveBeenCalledWith(mockError);

      // Cleanup
      consoleSpy.mockRestore();
    });

    it('should handle network errors gracefully', async () => {
      // Arrange
      const networkError = new Error('Network Error');
      mockAxiosService.axios.post.mockRejectedValue(networkError);

      // Spy on console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      const result = await authService.login('<EMAIL>', 'password123');

      // Assert
      expect(result).toEqual({ data: { token: '' } });
      expect(consoleSpy).toHaveBeenCalledWith(networkError);

      // Cleanup
      consoleSpy.mockRestore();
    });
  });

  describe('checkToken', () => {
    it('should return true for valid token with valid user profile', async () => {
      // Arrange
      const mockResponse = { data: { first_name: 'John', last_name: 'Doe' } };
      mockAxiosService.axios.get.mockResolvedValue(mockResponse);

      // Act
      const result = await authService.checkToken('valid-token');

      // Assert
      expect(mockAxiosService.axios.get).toHaveBeenCalledWith('/technician_profile', {
        headers: {
          Authorization: 'Bearer valid-token',
        },
      });
      expect(result).toEqual({ data: { isTokenValid: true } });
    });

    it('should return false for valid token with null first_name', async () => {
      // Arrange
      const mockResponse = { data: { first_name: null, last_name: 'Doe' } };
      mockAxiosService.axios.get.mockResolvedValue(mockResponse);

      // Act
      const result = await authService.checkToken('token-with-null-name');

      // Assert
      expect(mockAxiosService.axios.get).toHaveBeenCalledWith('/technician_profile', {
        headers: {
          Authorization: 'Bearer token-with-null-name',
        },
      });
      expect(result).toEqual({ data: { isTokenValid: false } });
    });

    it('should return false for invalid token (401 error)', async () => {
      // Arrange
      const mockError = { response: { status: 401 } };
      mockAxiosService.axios.get.mockRejectedValue(mockError);

      // Spy on console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      const result = await authService.checkToken('invalid-token');

      // Assert
      expect(mockAxiosService.axios.get).toHaveBeenCalledWith('/technician_profile', {
        headers: {
          Authorization: 'Bearer invalid-token',
        },
      });
      expect(result).toEqual({ data: { isTokenValid: false } });
      expect(consoleSpy).toHaveBeenCalledWith(mockError);

      // Cleanup
      consoleSpy.mockRestore();
    });

    it('should return false for network errors', async () => {
      // Arrange
      const networkError = new Error('Network Error');
      mockAxiosService.axios.get.mockRejectedValue(networkError);

      // Spy on console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      const result = await authService.checkToken('any-token');

      // Assert
      expect(result).toEqual({ data: { isTokenValid: false } });
      expect(consoleSpy).toHaveBeenCalledWith(networkError);

      // Cleanup
      consoleSpy.mockRestore();
    });

    it('should handle undefined first_name as invalid', async () => {
      // Arrange
      const mockResponse = { data: { last_name: 'Doe' } }; // first_name is undefined
      mockAxiosService.axios.get.mockResolvedValue(mockResponse);

      // Act
      const result = await authService.checkToken('token-undefined-name');

      // Assert
      expect(result).toEqual({ data: { isTokenValid: false } });
    });
  });

  describe('interface compliance', () => {
    it('should implement IAuthService interface correctly', () => {
      // Verify the service has the required methods
      expect(typeof authService.login).toBe('function');
      expect(typeof authService.checkToken).toBe('function');
    });
  });
});
