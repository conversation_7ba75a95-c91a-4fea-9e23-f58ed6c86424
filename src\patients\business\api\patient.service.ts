import { IPatient, PatientResponse } from '@/src/patients/business/types/patient-response';
import { PatientListParams } from '../types/patient-list-params';

export interface IGetPatients {
  execute(
    params: PatientListParams
  ): Promise<{ patients: IPatient[]; pagination: PatientResponse['pagination'] }>;
}

export abstract class IPatientService {
  abstract getPatients(params: PatientListParams): Promise<PatientResponse>;
}
