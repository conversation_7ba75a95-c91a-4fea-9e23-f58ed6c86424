export interface PatientResponse {
  data: PatientBundle[];
  pagination: Pagination;
  success: boolean;
}

export interface PatientBundle {
  id: string;
  name: Name[];
  active: boolean;
  birthDate: Date;
  extension: Extension[];
  identifier: Identifier[];
  resourceType: ResourceType;
  maritalStatus: MaritalStatus;
  managingOrganization: ManagingOrganization;
}

export interface Extension {
  url: string;
  valueCoding?: ValueCodingElement;
  valueInteger?: number;
}

export interface ValueCodingElement {
  code: string;
  display: ValueCodingDisplay | null;
}

export enum ValueCodingDisplay {
  Divorced = 'Divorced',
  Dni = 'DNI',
  Married = 'Married',
  PointClickCareID = 'PointClickCare ID',
  Widowed = 'Widowed',
}

export interface Identifier {
  type: Type;
  value?: Value;
  system: string;
}

export interface Type {
  coding: TypeCoding[];
}

export interface TypeCoding {
  code: Code;
  system: string;
}

export enum Code {
  Mr = 'MR',
  Ssn = 'SSN',
}

export enum Value {
  NA = 'N/A',
}

export interface ManagingOrganization {
  display: ManagingOrganizationDisplay;
  reference: unknown;
}

export enum ManagingOrganizationDisplay {
  Facility = 'Facility',
  PointClickCare44TrainFACILITY44 = 'PointClickCare-44: (train) FACILITY_44',
}
export interface MaritalStatus {
  coding: ValueCodingElement[];
}

export interface Name {
  given: string[];
  family: string;
}

export enum ResourceType {
  Patient = 'Patient',
}

export interface Pagination {
  current_page: number;
  total_pages: number;
  total_count: number;
}

export interface IPatient {
  id: string;
  name: string;
  dob: string;
  age: number | string;
  icd: string;
  alerts: number | string;
  status: string;
  room: string;
}
