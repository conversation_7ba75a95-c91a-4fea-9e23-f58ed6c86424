{"github.copilot.chat.commitMessageGeneration.instructions": [{"file": "docs/commit-message.md"}], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.preferences.includePackageJsonAutoImports": "off", "typescript.suggest.autoImports": true, "typescript.preferences.importModuleSpecifier": "absolute", "typescript.preferences.importModuleSpecifierEnding": "minimal", "typescript.suggest.completeFunctionCalls": true, "typescript.suggest.paths": true, "typescript.suggest.includeAutomaticOptionalChainCompletions": true, "typescript.workspaceSymbols.scope": "allOpenProjects", "typescript.updateImportsOnFileMove.enabled": "always", "javascript.preferences.importModuleSpecifier": "absolute", "javascript.suggest.autoImports": true, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}}