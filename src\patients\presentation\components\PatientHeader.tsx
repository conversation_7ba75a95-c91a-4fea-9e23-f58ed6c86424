import { COLORS } from '@/src/shared/business/constants/colors';
import { useBLE } from '@/src/shared/presentation/hooks/use-ble';
import { useDeviceStore } from '@/src/shared/presentation/store/devivce-store';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { memo, useEffect } from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface Props {
  patientName: string;
  handleConnectDevice: () => void;
}

const PatientHeaderComponent = ({ patientName, handleConnectDevice }: Props) => {
  const { activeDevice, resetActiveDevice } = useDeviceStore();
  const { disconnectFromDevice, checkConnection } = useBLE();
  const connectButtonText = activeDevice ? 'DeviceConnected' : 'Connect Device';
  const handleDisconnectDevice = () => {
    Alert.alert('Disconnect Device', 'Are you sure you want to disconnect the device?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Disconnect', onPress: () => disconnectFromDevice() },
    ]);
    disconnectFromDevice();
  };
  useEffect(() => {
    const checkConnectionFn = async () => {
      const isConnected = await checkConnection();
      console.log('isConnected', isConnected);
      if (!isConnected) {
        resetActiveDevice();
      }
    };
    checkConnectionFn();
  }, [activeDevice]);
  return (
    <View style={styles.header}>
      <View style={styles.headerTitleContainer}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={COLORS.TEXT_MAIN} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{patientName}</Text>
      </View>
      <View style={styles.headerButtons}>
        {/* <TouchableOpacity
          style={[styles.headerButton, { opacity: activeDevice ? 1 : 0.5 }]}
          onPress={handleSync}
          disabled={!activeDevice}
        >
          <Text style={styles.headerButtonText}>Sync</Text>
        </TouchableOpacity> */}
        <TouchableOpacity
          style={[styles.headerButton, { opacity: !!activeDevice ? 0.5 : 1 }]}
          onPress={handleConnectDevice}
          disabled={!!activeDevice}
        >
          <Text style={styles.headerButtonText}>{connectButtonText}</Text>
        </TouchableOpacity>
        {activeDevice && (
          <TouchableOpacity style={[styles.headerButton]} onPress={handleDisconnectDevice}>
            <Text style={styles.headerButtonText}>Disconnect</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerTitleContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 12,
    fontWeight: '700',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_MAIN,
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: 12,
  },
  headerButtons: {
    display: 'flex',
    flexDirection: 'row',
    padding: 16,
    gap: 19,
  },
  headerButton: {
    // backgroundColor: COLORS.ACCENT,
    borderWidth: 1,
    borderColor: COLORS.ACCENT,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 9999,
  },
  headerButtonText: {
    color: COLORS.ACCENT,
    fontSize: 9,
    fontWeight: '600',
    fontFamily: 'DMSans',
  },
});

export const PatientHeader = memo(PatientHeaderComponent);
