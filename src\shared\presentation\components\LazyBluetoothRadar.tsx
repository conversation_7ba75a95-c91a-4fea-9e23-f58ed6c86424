import React, { Suspense, lazy } from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import LazyLoadErrorBoundary from './LazyLoadErrorBoundary';

// Lazy load the BluetoothRadar component
const BluetoothRadarComponent = lazy(() =>
  import('./BluetoothRadar').then((module) => ({ default: module.BluetoothRadar }))
);

// Fallback component while loading
const BluetoothRadarFallback = () => (
  <View style={styles.fallbackContainer}>
    <ActivityIndicator size="large" color="#4A90E2" />
    <Text style={styles.fallbackText}>Initializing scanner...</Text>
  </View>
);

// Main lazy wrapper component
export const LazyBluetoothRadar = () => {
  return (
    <LazyLoadErrorBoundary>
      <Suspense fallback={<BluetoothRadarFallback />}>
        <BluetoothRadarComponent />
      </Suspense>
    </LazyLoadErrorBoundary>
  );
};

const styles = StyleSheet.create({
  fallbackContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 320,
  },
  fallbackText: {
    fontSize: 18,
    color: '#666',
    marginTop: 20,
    textAlign: 'center',
  },
});

export default LazyBluetoothRadar;
