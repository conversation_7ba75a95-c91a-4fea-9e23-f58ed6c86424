import { DEVICE_CHARACTERISTICS } from '@/src/shared/business/constants/ble-constants';
import { Device } from 'react-native-ble-plx';
export interface HeartRateData {
  heartRate: number;
  energyExpended?: number;
  rrIntervals?: number[];
  sensorContact: boolean;
  timestamp: Date;
}

// Enhanced heart rate data types based on J Style API documentation
export interface SmartRingHeartRateReading {
  heartRate: number;
  timestamp: Date;
}

export interface SmartRingContinuousHeartRateData {
  commandType: 0x54;
  dataId: number;
  timestamp: Date;
  readings: SmartRingHeartRateReading[]; // 15 readings for 15 minutes
}

export interface SmartRingSingleHeartRateData {
  commandType: 0x55;
  dataId: number;
  timestamp: Date;
  heartRate: number;
}

export type SmartRingHeartRateResponse =
  | SmartRingContinuousHeartRateData
  | SmartRingSingleHeartRateData;

// Command parameters for heart rate requests
export interface HeartRateRequestParams {
  action: 'delete_all' | 'read_latest' | 'read_at_location' | 'continue_from_last';
  location?: number; // Used when action is 'read_at_location'
}

export interface HeartRateData {
  heartRate: number;
  energyExpended?: number;
  rrIntervals?: number[];
  sensorContact: boolean;
  timestamp: Date;
}

export interface BleConnectionOptions {
  requestMTU?: number;
  timeout?: number;
}

export interface BlePermissionStatus {
  granted: boolean;
  message?: string;
}

export type BleState =
  | 'Unknown'
  | 'Resetting'
  | 'Unsupported'
  | 'Unauthorized'
  | 'PoweredOff'
  | 'PoweredOn';

export interface BleDeviceInfo {
  id: string;
  name?: string;
  localName?: string;
  rssi?: number;
  isConnectable?: boolean;
}

export interface BleScanOptions {
  allowDuplicates?: boolean;
  scanMode?: number;
  timeout?: number;
  heartRateFirst?: boolean;
}

// Additional Smart Ring data types (J Style SDK)
export interface SmartRingBatteryData {
  batteryLevel: number; // 0-100%
  timestamp: Date;
  rawData?: number[];
}

export interface SmartRingOxygenData {
  oxygenLevel: number | null; // SpO2 percentage
  timestamp: Date;
  rawData: number[];
}

export interface SmartRingActivityData {
  totalSteps: number | null;
  calories?: number;
  distance?: number;
  activeTime?: number;
  timestamp: Date;
  rawData: number[];
}

// Device status information
export interface SmartRingDeviceStatus {
  batteryLevel?: number;
  isConnected: boolean;
  firmwareVersion?: string;
  deviceName?: string;
  lastSync?: Date;
}

// Live measurement parameters (based on official SDK SetDeviceMeasurementWithType)
export interface LiveMeasurementParams {
  measurementType: 2 | 3; // 2=Heart Rate (AutoHeartRate), 3=Oxygen (AutoSpo2)
  durationSeconds: number;
}

// Live measurement response data
export interface LiveMeasurementResponse {
  commandType: 0x28; // MeasurementWithType
  measurementType: 2 | 3;
  isComplete: boolean;
  timestamp: Date;
  data: {
    heartRate?: number;
    oxygenLevel?: number;
    [key: string]: any; // Allow for additional measurement data
  };
  rawData: number[];
}

// Live measurement stop response
export interface LiveMeasurementStopResponse {
  commandType: 0x28;
  measurementType: 2 | 3;
  isComplete: boolean;
  timestamp: Date;
}

// ========================================================
export type DeviceType = 'ring' | 'oximeter' | 'bracelet' | 'unknown';

// Unified health data interface
export interface HealthData {
  heartRate?: number;
  spo2?: number;
  timestamp: Date;
  deviceType: DeviceType;
  // Additional fields for extensibility
  hrv?: number;
  fatigue?: number;
  rawData?: number[];
}

// Measurement types
export type MeasurementType = 'heart_rate' | 'spo2' | 'combined';

// Connected device info
export interface ConnectedDeviceInfo {
  device: Device;
  type: DeviceType;
  characteristics: (typeof DEVICE_CHARACTERISTICS)[keyof typeof DEVICE_CHARACTERISTICS];
}
