import { coreContainer } from '@/src/core/dependency-injection/inversify.config';
import { Container } from 'inversify';
import { FACILITY_DEPENDENCY_INJECTION_TYPES } from '../business/types/facility-symbols';
import { GetFacilities } from '../capabilities/get-facilities';
import { FacilityService } from './api/facility.service';

const facilityContainer = new Container({ parent: coreContainer });

facilityContainer.bind(FACILITY_DEPENDENCY_INJECTION_TYPES.FacilityService).to(FacilityService);
facilityContainer.bind(FACILITY_DEPENDENCY_INJECTION_TYPES.GetFacilities).to(GetFacilities);
export { facilityContainer };
