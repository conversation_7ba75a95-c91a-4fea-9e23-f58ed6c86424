import React, { Suspense, lazy } from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';

// Lazy load the ReportingView component
const ReportingViewComponent = lazy(() => import('./ReportingView'));

// Fallback component while loading
const ReportingViewFallback = () => (
  <View style={styles.fallbackContainer}>
    <View style={styles.headerSection}>
      <View style={styles.titlePlaceholder} />
      <View style={styles.statusPlaceholder} />
    </View>

    <View style={styles.controlSection}>
      <View style={styles.buttonPlaceholder} />
    </View>

    <View style={styles.loadingIndicator}>
      <ActivityIndicator size="large" color="#4A90E2" />
      <Text style={styles.loadingText}>Loading Reporting Module...</Text>
    </View>
  </View>
);

// Main lazy wrapper component
export const LazyReportingView = () => {
  return (
    <Suspense fallback={<ReportingViewFallback />}>
      <ReportingViewComponent />
    </Suspense>
  );
};

const styles = StyleSheet.create({
  fallbackContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    padding: 16,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  titlePlaceholder: {
    width: 250,
    height: 32,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    marginBottom: 8,
  },
  statusPlaceholder: {
    width: 180,
    height: 20,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
  },
  controlSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  buttonPlaceholder: {
    width: 200,
    height: 48,
    backgroundColor: '#e5e7eb',
    borderRadius: 8,
  },
  loadingIndicator: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
});

export default LazyReportingView;
