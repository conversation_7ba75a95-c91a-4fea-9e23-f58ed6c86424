import { Image, StyleSheet, View } from 'react-native';

export default function HeaderLogo() {
  return (
    <View style={styles.drawerHeader}>
      <Image
        source={require('../../../../assets/images/logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  drawerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 70,
    paddingBottom: 20,
  },
  logo: {
    height: 70,
    width: 200,
    flex: 1,
  },
});
