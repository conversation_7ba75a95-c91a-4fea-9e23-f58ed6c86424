import { Facility } from '@/src/facilities/business/types/facility-response';

export const useFacilityInfo = (facility: Facility) => {
  const srNo = facility.extension?.[0]?.valueString;
  const name = facility.name;
  const numberOrPatients = facility.extension.reduce((acc, curr) => {
    if (curr.url === 'Patient Count') {
      return acc + (curr.valueInteger || 0);
    }
    return acc;
  }, 0);
  const numberOfDevices = facility.extension.reduce((acc, curr) => {
    if (curr.url === 'Device Count') {
      return acc + (curr.valueInteger || 0);
    }
    return acc;
  }, 0);
  const numberOfAlerts = facility.extension.reduce((acc, curr) => {
    if (curr.url === 'Critical Alert Count') {
      return acc + (curr.valueInteger || 0);
    }
    if (curr.url === 'Warning Alert Count') {
      return acc + (curr.valueInteger || 0);
    }
    if (curr.url === 'Pending Reading Alert Count') {
      return acc + (curr.valueInteger || 0);
    }
    if (curr.url === 'Normal Alert Count') {
      return acc + (curr.valueInteger || 0);
    }
    return acc;
  }, 0);
  const address = facility.type?.[0]?.coding?.[0]?.display;

  return { srNo, name, numberOrPatients, numberOfDevices, numberOfAlerts, address };
};
