import { Status, Unit } from '@/src/patients/business/types/metric-types';
import { MetricGraph } from '@/src/patients/presentation/components/MetricGraph';
import { COLORS } from '@/src/shared/business/constants/colors';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { RecordsTable } from './RecordsTable';
import { ReportingComponent } from './ReportingComponent';
import { SettingsComponent } from './SettingsComponent';
interface Props {
  unit: Unit;
  metric: string | React.ReactNode;
  value: number;
  max: number;
  min: number;
  status: Status;
  onSync: () => void;
}
export const MetricCard = ({ unit, value, max, min, status, metric, onSync }: Props) => {
  const [maxValue, setMaxValue] = useState(max);
  const [minValue, setMinValue] = useState(min);
  const [isModalSettingsVisible, setIsModalSettingsVisible] = useState(false);
  const [isModalReportingVisible, setIsModalReportingVisible] = useState(false);

  const handleReportingPress = () => {
    setIsModalReportingVisible(true);
  };

  const handleCloseReportingModal = () => {
    setIsModalReportingVisible(false);
  };
  const handleSettingsPress = () => {
    setIsModalSettingsVisible(true);
  };

  const handleCloseSettingsModal = () => {
    setIsModalSettingsVisible(false);
  };
  const handleMaxValueChange = (value: number) => {
    setMaxValue(value);
  };
  const handleMinValueChange = (value: number) => {
    setMinValue(value);
  };
  return (
    <View style={styles.card}>
      {/* Card Header  */}
      <View style={styles.cardHeader}>
        <View style={styles.cardHeaderTitle}>
          <Text style={styles.cardHeaderTitleText}>{metric}</Text>
        </View>
        <View style={styles.cardHeaderSyncButton}>
          <TouchableOpacity onPress={onSync} style={styles.cardHeaderSyncButtonTouchable}>
            <Text style={styles.cardHeaderSyncButtonText}> Sync </Text>
            <Ionicons name="sync" size={12} color={COLORS.WHITE} />
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.cardBody}>
        {/* Results - Value max - Min Value */}
        <View style={styles.cardBodyResults}>
          <View style={styles.resultCard}>
            <View style={styles.resultCardValueContainer}>
              <Text style={styles.result}>{value}</Text>
              <Text style={styles.unit}>{unit}</Text>
            </View>
            <Text style={styles.resultCardValue}>{status}</Text>
          </View>
          {/* Max - Min Value */}
          <View style={styles.cardBodyMaxMinValue}>
            <View style={styles.cardBodyMaxMinValueItem}>
              <Text style={styles.cardBodyMaxMinValueItemText}>Maximum</Text>
              <Text style={styles.cardBodyMaxMinValueItemValue}>{maxValue}</Text>
              <Text style={styles.cardBodyMaxMinValueItemUnit}>{unit}</Text>
            </View>
            <View style={styles.cardBodyMaxMinValueItem}>
              <Text style={styles.cardBodyMaxMinValueItemText}>Minimum</Text>
              <Text style={styles.cardBodyMaxMinValueItemValue}>{minValue}</Text>
              <Text style={styles.cardBodyMaxMinValueItemUnit}>{unit}</Text>
            </View>
          </View>
        </View>
        <View style={styles.cardBodyGraph}>
          <MetricGraph max={maxValue} min={minValue} status={status} />
          {/* Settings Button */}
          <View style={styles.cardBodyGraphSettings}>
            <TouchableOpacity
              style={styles.cardBodyGraphSettingsButton}
              onPress={handleSettingsPress}
            >
              <Ionicons name="settings-outline" size={16} color={COLORS.WHITE} />
            </TouchableOpacity>
          </View>
          {/* Settings Modal */}
          <SettingsComponent
            visible={isModalSettingsVisible}
            onClose={handleCloseSettingsModal}
            maxValue={maxValue}
            minValue={minValue}
            onMaxValueChange={handleMaxValueChange}
            onMinValueChange={handleMinValueChange}
          />
          {/* Reporting Modal */}
          <ReportingComponent
            isVisible={isModalReportingVisible}
            onClose={handleCloseReportingModal}
            title={metric as string}
          />
          {/* Records Table */}
          <RecordsTable />
          <View style={styles.cardBodyGraphSettings}>
            <TouchableOpacity style={styles.cardBodyReportButton} onPress={handleReportingPress}>
              <Text style={styles.cardBodyGraphSettingsButtonText}>Report</Text>
              <Ionicons name="document-text-outline" size={12} color={COLORS.WHITE} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: COLORS.LIGHT_WHITE,
    padding: 8,
    borderRadius: 16,
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },
  cardHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  cardHeaderTitle: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
    alignItems: 'flex-start',
  },
  cardHeaderTitleText: {
    fontSize: 12,
    fontWeight: '700',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_TITLE,
  },
  cardHeaderValue: {
    fontSize: 24,
    fontWeight: '400',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_MAIN,
  },
  cardHeaderStatus: {
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'DMSans',
    color: COLORS.GREEN,
  },
  cardHeaderSyncButton: {
    backgroundColor: COLORS.ACCENT,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 9999,
  },
  cardHeaderSyncButtonTouchable: {
    display: 'flex',
    flexDirection: 'row',
    gap: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardHeaderSyncButtonText: {
    color: COLORS.WHITE,
    fontSize: 7,
    fontWeight: '600',
    fontFamily: 'DMSans',
  },
  cardBody: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  cardBodyResults: {
    display: 'flex',
    justifyContent: 'space-between',
    paddingRight: 16,
    width: '100%',
    borderRadius: 16,
    backgroundColor: COLORS.BACKGROUND_GRAY,
    flexDirection: 'row',
    gap: 16,
  },
  resultCard: {
    maxWidth: '50%',
    display: 'flex',
    elevation: 2,
    shadowColor: COLORS.TEXT_MAIN,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    borderRadius: 12,
    backgroundColor: COLORS.WHITE,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  resultCardValueContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 1,
    justifyContent: 'flex-start',
  },
  result: {
    fontSize: 16,
    fontWeight: '700',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_MAIN,
  },
  unit: {
    fontSize: 8,
  },
  resultCardValue: {
    fontSize: 8,
    fontWeight: '400',
    fontFamily: 'DMSans',
    color: COLORS.GREEN,
  },
  cardBodyMaxMinValue: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  cardBodyMaxMinValueItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 2,
    flexWrap: 'wrap',
  },
  cardBodyMaxMinValueItemText: {
    fontSize: 7,
    fontWeight: '400',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_MAIN,
    textAlign: 'left',
  },
  cardBodyMaxMinValueItemValue: {
    fontSize: 10,
  },
  cardBodyMaxMinValueItemUnit: {
    fontSize: 7,
    fontWeight: '400',
    fontFamily: 'DMSans',
    color: COLORS.TEXT_MAIN,
  },
  cardBodyGraph: {
    display: 'flex',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardBodyGraphSettings: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: '100%',
    padding: 6,
  },
  cardBodyGraphSettingsButton: {
    padding: 3,
    borderRadius: 9999,
    backgroundColor: COLORS.ACCENT,
  },
  cardBodyGraphSettingsButtonText: {
    fontSize: 7,
    fontWeight: '600',
    fontFamily: 'DMSans',
    color: COLORS.WHITE,
  },
  cardBodyReportButton: {
    padding: 4,
    borderRadius: 9999,
    backgroundColor: COLORS.ACCENT,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 4,
  },
});
