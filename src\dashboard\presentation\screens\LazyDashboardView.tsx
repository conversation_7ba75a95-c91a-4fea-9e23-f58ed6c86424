import React, { Suspense, lazy } from 'react';
import { StyleSheet, View } from 'react-native';

// Lazy load the DashboardView component
const DashboardViewComponent = lazy(() => import('./DashboardView'));

// Fallback component while loading
const DashboardViewFallback = () => (
  <View style={styles.fallbackContainer}>
    <View style={styles.headerSection}>
      <View style={styles.greetingPlaceholder} />
      <View style={styles.iconsPlaceholder} />
    </View>

    <View style={styles.sectionPlaceholder}>
      <View style={styles.sectionTitlePlaceholder} />
      <View style={styles.tablePlaceholder} />
    </View>

    <View style={styles.sectionPlaceholder}>
      <View style={styles.sectionTitlePlaceholder} />
      <View style={styles.alertsPlaceholder} />
    </View>
  </View>
);

// Main lazy wrapper component
export const LazyDashboardView = () => {
  return (
    <Suspense fallback={<DashboardViewFallback />}>
      <DashboardViewComponent />
    </Suspense>
  );
};

const styles = StyleSheet.create({
  fallbackContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    padding: 16,
  },
  headerSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  greetingPlaceholder: {
    width: 200,
    height: 24,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
  },
  iconsPlaceholder: {
    width: 80,
    height: 24,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
  },
  sectionPlaceholder: {
    backgroundColor: '#FFFFFF80',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitlePlaceholder: {
    width: 150,
    height: 20,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    marginBottom: 16,
  },
  tablePlaceholder: {
    height: 120,
    backgroundColor: '#e5e7eb',
    borderRadius: 8,
  },
  alertsPlaceholder: {
    height: 80,
    backgroundColor: '#e5e7eb',
    borderRadius: 8,
  },
  loadingIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
});

export default LazyDashboardView;
