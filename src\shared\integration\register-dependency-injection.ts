import { coreContainer } from '@/src/core/dependency-injection/inversify.config';
import { Container } from 'inversify';
import { SHARED_DEPENDENCY_INJECTION_TYPES } from '../business/types/shared-symbols';
import { BleManagerService } from './ble/ble-manager.service';
import { LoggerService } from './logger/logger.service';

const sharedContainer = new Container({ parent: coreContainer });

sharedContainer.bind(SHARED_DEPENDENCY_INJECTION_TYPES.BleManager).to(BleManagerService);
sharedContainer.bind(SHARED_DEPENDENCY_INJECTION_TYPES.Logger).to(LoggerService);

export { sharedContainer };
