import { BleError, Characteristic, Device } from 'react-native-ble-plx';
import {
  BleConnectionOptions,
  BlePermissionStatus,
  BleScanOptions,
  BleState,
  HeartRateData,
  HeartRateRequestParams,
  LiveMeasurementParams,
  LiveMeasurementResponse,
  LiveMeasurementStopResponse,
  SmartRingActivityData,
  SmartRingHeartRateResponse,
  SmartRingOxygenData,
} from '../types/ble-types';

export interface IBleManager {
  // State management
  getState(): Promise<BleState>;
  onStateChange(callback: (state: BleState) => void): () => void;

  // Permissions
  requestPermissions(): Promise<BlePermissionStatus>;

  // Device scanning
  startDeviceScan(
    serviceUUIDs?: string[] | null,
    options?: BleScanOptions,
    callback?: (error: BleError | null, device: Device | null) => void
  ): void;
  stopDeviceScan(): void;

  // Device connection
  connectToDevice(deviceId: string, options?: BleConnectionOptions): Promise<Device>;
  disconnectFromDevice(deviceId: string): Promise<void>;
  isDeviceConnected(deviceId: string): Promise<boolean>;
  onDeviceDisconnected(
    deviceId: string,
    callback: (error: BleError | null, device: Device | null) => void
  ): () => void;

  // Service discovery
  discoverAllServicesAndCharacteristics(deviceId: string): Promise<Device>;
  getServices(deviceId: string): Promise<any[]>;

  // Legacy heart rate methods (for backward compatibility)
  startHeartRateMonitoring(
    deviceId: string,
    callback: (error: BleError | null, data: HeartRateData | null) => void
  ): Promise<void>;
  stopHeartRateMonitoring(deviceId: string): Promise<void>;
  resetEnergyExpended(deviceId: string): Promise<void>;
  readSensorLocation(deviceId: string): Promise<string | null>;

  // New Smart Ring heart rate methods (based on J Style API 2301)
  requestHeartRateData(
    deviceId: string,
    params: HeartRateRequestParams
  ): Promise<SmartRingHeartRateResponse>;
  requestSingleHeartRateData(
    deviceId: string,
    params: HeartRateRequestParams
  ): Promise<SmartRingHeartRateResponse>;
  requestSingleHeartRateDataWithNotification(
    deviceId: string,
    params: HeartRateRequestParams
  ): Promise<SmartRingHeartRateResponse>;
  requestSingleHeartRateDataDirect(
    deviceId: string,
    params: HeartRateRequestParams
  ): Promise<SmartRingHeartRateResponse>;
  deleteAllHeartRateData(deviceId: string): Promise<void>;

  // Additional Smart Ring functionality (J Style SDK)
  getBatteryLevel(deviceId: string): Promise<number>;
  getOxygenData(deviceId: string): Promise<SmartRingOxygenData>;
  getTotalActivityData(deviceId: string): Promise<SmartRingActivityData>;
  setDeviceTime(deviceId: string, time?: Date): Promise<void>;

  // Live measurement methods (based on official SDK MeasurementWithType)
  startLiveMeasurement(
    deviceId: string,
    params: LiveMeasurementParams
  ): Promise<LiveMeasurementResponse>;
  stopLiveMeasurement(deviceId: string, measurementType: 1 | 2 | 3): Promise<void>;
  monitorLiveMeasurement(
    deviceId: string,
    callback: (
      error: BleError | null,
      data: LiveMeasurementResponse | LiveMeasurementStopResponse | null
    ) => void
  ): () => void;

  // Connection status for debugging
  getConnectionStatus(): { isConnected: boolean; deviceId: string | null; isConnecting: boolean };

  // Testing and diagnostics
  testConnection(deviceId: string): Promise<string | null>;

  // Generic characteristic operations
  monitorCharacteristic(
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string,
    callback: (error: BleError | null, characteristic: Characteristic | null) => void
  ): void;
  readCharacteristic(
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string
  ): Promise<Characteristic>;
  writeCharacteristic(
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string,
    data: string
  ): Promise<Characteristic>;
}
