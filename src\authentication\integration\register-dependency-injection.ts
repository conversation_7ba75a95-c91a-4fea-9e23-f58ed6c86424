import { coreContainer } from '@/src/core/dependency-injection/inversify.config';
import { Container } from 'inversify';
import { AUTH_DEPENDENCY_INJECTION_TYPES } from '../business/types/auth-symbols';
import { AuthService } from './api/auth.service';

const authContainer = new Container({ parent: coreContainer });

authContainer.bind(AUTH_DEPENDENCY_INJECTION_TYPES.AuthService).to(AuthService);

export { authContainer };
