import { LogEntry } from '@/src/shared/business/types/logger-types';

export abstract class ILogger {
  abstract log(entry: Omit<LogEntry, 'timestamp'>): Promise<void>;
  abstract error(
    message: string,
    source: string,
    metadata?: Record<string, unknown>
  ): Promise<void>;
  abstract warn(message: string, source: string, metadata?: Record<string, unknown>): Promise<void>;
  abstract info(message: string, source: string, metadata?: Record<string, unknown>): Promise<void>;
  abstract debug(
    message: string,
    source: string,
    metadata?: Record<string, unknown>
  ): Promise<void>;
  abstract getRecentLogs(filters?: {
    level?: string;
    source?: string;
    limit?: number;
  }): Promise<LogEntry[]>;
  abstract healthCheck(): Promise<boolean>;
}
