{"name": "rpm-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "tsc --noEmit", "commit": "cz"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"@expo-google-fonts/dm-sans": "^0.4.1", "@expo-google-fonts/inter": "^0.4.1", "@expo-google-fonts/poppins": "^0.4.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "5.81.5", "axios": "1.10.0", "expo": "53.0.19", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-image": "~2.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "inversify": "^7.5.4", "inversify-hooks": "^2.1.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "7.59.0", "react-native": "0.79.5", "react-native-base64": "^0.2.1", "react-native-ble-plx": "^3.5.0", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.63", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-toast-message": "^2.3.3", "react-native-web": "~0.20.0", "reflect-metadata": "^0.2.2", "tailwindcss": "^3.4.17", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.27.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native-base64": "^0.2.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "babel-plugin-transform-typescript-metadata": "^0.3.2", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.30.0", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~53.0.9", "prettier-plugin-tailwindcss": "^0.6.14", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}