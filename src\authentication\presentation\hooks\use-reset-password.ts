import { useDependencyInjection } from '@/src/core/hooks/use-dependency-injection';
import { useMutation } from '@tanstack/react-query';
import { router } from 'expo-router';
import Toast from 'react-native-toast-message';
import { IAuthService } from '../../business/api/auth.service';
import { AUTH_DEPENDENCY_INJECTION_TYPES } from '../../business/types/auth-symbols';
import { authContainer } from '../../integration/register-dependency-injection';

export const useResetPassword = () => {
  const authService: IAuthService = useDependencyInjection(
    authContainer,
    AUTH_DEPENDENCY_INJECTION_TYPES.AuthService
  );

  const mutation = useMutation({
    mutationFn: (email: string) => authService.forgotPassword(email),
    onSuccess: (response) => {
      //TODO: For now api is resolving with empty data object so it will always go to catch block
      if (response?.data && Object.keys(response.data).length > 0) {
        Toast.show({
          type: 'success',
          text1: 'Password reset email sent',
          text2: 'Check your email for reset instructions.',
        });
        router.back();
      } else {
        throw new Error('Invalid email');
      }
    },
    onError: () => {
      // TODO: remove Success toast and show error toast after actual api response
      Toast.show({
        type: 'success',
        text1: 'Password reset email sent',
        text2: 'Check your email for reset instructions.',
      });
      router.back();
      // Toast.show({
      //   type: 'error',
      //   text1: 'Invalid email',
      //   text2: 'This email is not registered.',
      // });
    },
  });

  // Rename for clarity when using in components
  return {
    handleResetPassword: mutation.mutate,
    isLoading: mutation.isPending,
  };
};
