import { inject, injectable } from 'inversify';
import { IGetFacilities } from '../business/api/facility.service';
import { FacilityResponse } from '../business/types/facility-response';
import { FACILITY_DEPENDENCY_INJECTION_TYPES } from '../business/types/facility-symbols';
import { FacilityService } from '../integration/api/facility.service';

@injectable()
export class GetFacilities implements IGetFacilities {
  constructor(
    @inject(FACILITY_DEPENDENCY_INJECTION_TYPES.FacilityService)
    private readonly facilityService: FacilityService
  ) {}

  async execute(search: string): Promise<FacilityResponse> {
    return await this.facilityService.getFacilities(search);
  }
}
