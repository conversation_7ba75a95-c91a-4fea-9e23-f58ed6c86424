import { AUTH_DEPENDENCY_INJECTION_TYPES } from '@/src/authentication/business/types/auth-symbols';
import { createContext, use, useEffect, type PropsWithChildren } from 'react';
import { useDependencyInjection } from '../../../core/hooks/use-dependency-injection';
import { useStorageState } from '../../../shared/presentation/hooks/use-storage-state';
import { IAuthService } from '../../business/api/auth.service';
import { AUTH_TOKEN_KEY } from '../../business/constants';
import { authContainer } from '../../integration/register-dependency-injection';

const AuthContext = createContext<{
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => void;
  session?: string | null;
  isLoading: boolean;
  resetPassword: (email: string) => Promise<{ data: any }>;
}>({
  signIn: () => Promise.resolve(),
  signOut: () => null,
  session: null,
  isLoading: false,
  resetPassword: () => Promise.resolve({ data: {} }),
});

// This hook can be used to access the user info.
export function useSession() {
  const value = use(AuthContext);
  if (!value) {
    throw new Error('useSession must be wrapped in a <SessionProvider />');
  }

  return value;
}

export function SessionProvider({ children }: PropsWithChildren) {
  const [[isLoading, session], setSession] = useStorageState(AUTH_TOKEN_KEY);
  const authService: IAuthService = useDependencyInjection(
    authContainer,
    AUTH_DEPENDENCY_INJECTION_TYPES.AuthService
  );
  // if there is a token, first check if its valid other wise redirect to login
  useEffect(() => {
    if (session) {
      authService.checkToken(session).then((response) => {
        if (!response.data.isTokenValid) {
          setSession(null);
        }
      });
    }
  }, [session]);

  return (
    <AuthContext
      value={{
        signIn: async (email: string, password: string) => {
          const response = await authService.login(email, password);
          if (response.data.token === '') {
            throw new Error('Invalid credentials');
          }
          setSession(response.data.token);
        },
        signOut: () => {
          setSession(null);
        },
        resetPassword: async (email: string) => {
          const response: any = await authService.forgotPassword(email);
          if (response) {
            return response;
          }
        },
        session,
        isLoading,
      }}
    >
      {children}
    </AuthContext>
  );
}
