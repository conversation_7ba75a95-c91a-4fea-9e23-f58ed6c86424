# RPM App

A React Native tablet application built with Expo, TypeScript, and Domain-Driven Design (DDD) architecture for Remote Patient Monitoring (RPM) healthcare solutions.

## 📱 Project Status

**Current Version:** 1.0.0  
**Target Platform:** Tablets (iOS, Android, Web)  
**Architecture:** Domain-Driven Design (DDD) with clean separation of concerns  
**Development Status:** Active Development

### Implemented Features ✅
- **Authentication System** - Login/logout with secure token management
- **Dashboard** - Main overview screen with navigation
- **Facilities Management** - Complete CRUD operations with search functionality
- **Patients Management** - Patient data viewing and management
- **Reporting Module** - Basic reporting structure (in development)

### In Development 🚧
- Advanced reporting features
- Real-time data synchronization
- Offline capabilities
- Push notifications

## ✨ Key Features

- 🚀 **Expo SDK 53** - Latest Expo SDK with new architecture support
- 📱 **Expo Router** - File-based routing with drawer navigation
- 🔷 **TypeScript** - Strict type checking with experimental decorators
- 🏗️ **Domain-Driven Design** - Clean architecture with clear domain boundaries
- 💉 **Dependency Injection** - Inversify container for clean dependency management
- 🎯 **Absolute Imports** - Clean import statements using `@/` prefix
- 🔄 **React Query** - Server state management with caching and synchronization
- 🎨 **Custom Fonts** - Inter, Poppins, and DM Sans font families
- 📱 **Tablet Optimized** - Responsive design specifically for tablet interfaces
- ✅ **Code Quality** - ESLint, Prettier, Husky, and Conventional Commits
- 🔐 **Secure Storage** - Token management with Expo Secure Store

## 🛠️ Technology Stack

### Core Technologies
- **React Native** (0.79.5) - Cross-platform mobile development
- **Expo** (53.0.16) - Development platform and build tools
- **TypeScript** (5.8.3) - Static type checking
- **Expo Router** (5.1.2) - File-based navigation system

### State Management & Data
- **React Query (TanStack)** (5.81.5) - Server state management
- **Zustand** (5.0.6) - Client state management
- **AsyncStorage** - Local data persistence
- **Expo Secure Store** - Secure token storage

### Architecture & DI
- **Inversify** (7.5.4) - Dependency injection container
- **Reflect Metadata** - Decorator metadata support
- **Domain-Driven Design** - Clean architecture patterns

### UI & Styling
- **React Hook Form** (7.59.0) - Form management
- **Expo Vector Icons** - Icon library
- **Custom Fonts** - Inter, Poppins, DM Sans
- **React Native Reanimated** - Smooth animations
- **React Native Gesture Handler** - Touch interactions

### Development Tools
- **ESLint** (9.30.0) - Code linting
- **Prettier** (10.1.5) - Code formatting
- **Husky** (9.1.7) - Git hooks
- **Commitizen** - Conventional commits
- **Jest** - Testing framework

### Network & API
- **Axios** (1.10.0) - HTTP client
- **Network Info** - Connection status monitoring

## 📋 Prerequisites

Before running this project, ensure you have:

- **Node.js** (18.0.0 or higher)
- **npm** or **yarn**
- **Expo CLI** (`npm install -g @expo/cli`)
- **Git** for version control

### Development Environment
- **iOS Simulator** (for iOS development on macOS)
- **Android Studio & Emulator** (for Android development)
- **Expo Go app** (for physical device testing)

## 🚀 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd RPM_APP
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
The project is configured for tablet optimization and includes network security configurations for API communication.

## 🎮 Running the Application

### Development Server
Start the Expo development server:
```bash
npm start
```

This opens Expo Developer Tools. From there:
- Press `i` for iOS Simulator
- Press `a` for Android Emulator  
- Press `w` for Web browser
- Scan QR code with Expo Go app on physical device

### Platform-Specific Commands
```bash
# iOS Simulator
npm run ios

# Android Emulator  
npm run android

# Web Browser
npm run web
```

### Development Scripts
```bash
# Code quality checks
npm run type-check      # TypeScript checking
npm run lint           # ESLint checking
npm run lint:fix       # Auto-fix ESLint issues
npm run format         # Format with Prettier
npm run format:check   # Check Prettier formatting

# Testing
npm test              # Run Jest tests

# Commit assistance
npm run commit        # Interactive conventional commits
```

## 🏗️ Project Architecture - Domain-Driven Design (DDD)

This project implements Domain-Driven Design with clear separation of concerns across multiple layers:

### High-Level Structure
```
RPM_APP/
├── app/                    # 🛣️  Expo Router (File-based routing)
├── src/                    # 🏗️  Business logic & domains
├── assets/                 # 🎨  Static assets (images, fonts)
├── components/             # 📦  Legacy components (being migrated)
└── constants/              # 📝  Legacy constants (being migrated)
```

### Detailed Domain Structure

```
src/
├── shared/                 # 🔄 Cross-domain shared resources
│   ├── hooks/             # Custom React hooks
│   │   ├── use-app-state.ts
│   │   ├── use-online-manager.ts
│   │   ├── use-referesh-on-focus.ts
│   │   └── use-storage-state.ts
│   └── presentation/
│       └── components/    # Reusable UI components
│           ├── CustomDrawerContent.tsx
│           ├── DataCard.tsx
│           ├── DataList.tsx
│           ├── DataTable.tsx
│           ├── DrawerNavigation.tsx
│           ├── HeaderLogo.tsx
│           ├── LogoutSection.tsx
│           └── SearchForm.tsx
│
├── core/                   # 🏛️ Application core infrastructure
│   ├── dependency-injection/
│   │   ├── inversify.config.ts
│   │   └── types.ts
│   ├── hooks/
│   │   └── use-dependency-injection.ts
│   └── services/
│       └── axios.service.ts
│
├── authentication/         # 🔐 Authentication domain
│   ├── business/
│   │   ├── api/
│   │   │   └── auth.service.ts
│   │   ├── constants/
│   │   │   └── index.ts
│   │   └── types/
│   │       └── auth-symbols.ts
│   ├── integration/
│   │   ├── api/
│   │   │   └── auth.service.ts
│   │   └── register-dependency-injection.ts
│   └── presentation/
│       └── context/
│           └── AuthContext.tsx
│
├── dashboard/              # 📊 Dashboard domain
│   └── presentation/
│       └── screens/
│           └── DashboardView.tsx
│
├── facilities/             # 🏥 Facilities management domain
│   ├── business/
│   │   ├── api/
│   │   │   └── facility.service.ts
│   │   └── types/
│   │       ├── facility-response.ts
│   │       └── facility-symbols.ts
│   ├── capabilities/
│   │   └── get-facilities.ts
│   ├── integration/
│   │   ├── api/
│   │   │   └── facility.service.ts
│   │   └── register-dependency-injections.ts
│   └── presentation/
│       ├── components/
│       │   ├── facility-list.tsx
│       │   ├── facility-table-header.tsx
│       │   └── facility-table-row.tsx
│       ├── hooks/
│       │   ├── use-facility-formatted-info.ts
│       │   └── use-facility-list.ts
│       └── screens/
│           └── FacilitiesView.tsx
│
├── patients/               # 👥 Patient management domain
│   ├── business/
│   │   ├── api/
│   │   │   └── patient.service.ts
│   │   └── types/
│   │       └── patient-response.ts
│   ├── integration/
│   │   ├── api/
│   │   │   └── patient.service.ts
│   │   └── register-dependency-injection.ts
│   └── presentation/
│       ├── components/
│       │   ├── PatientHeader.tsx
│       │   ├── PatientList.tsx
│       │   ├── PatientSearchHeader.tsx
│       │   └── PatientsTitle.tsx
│       └── screens/
│           └── PatientView.tsx
│
└── reporting/              # 📈 Reporting domain
    └── presentation/
        └── screens/
            └── ReportingView.tsx
```

### Routing Structure (Expo Router)
```
app/
├── _layout.tsx            # Root layout with providers
├── index.tsx              # App entry point
├── (auth)/                # Authentication group
│   ├── _layout.tsx        # Auth layout
│   └── login.tsx          # Login screen
└── (app)/                 # Main app group (protected)
    ├── _layout.tsx        # Drawer navigation layout
    ├── index.tsx          # Dashboard route
    ├── facilities.tsx     # Facilities route
    ├── patients.tsx       # Patients route
    └── reporting.tsx      # Reporting route
```

### DDD Layer Responsibilities

#### 🎨 Presentation Layer
- **Purpose**: UI components, screens, user interactions
- **Contents**: React components, hooks, screens, navigation
- **Dependencies**: Can import from Business and Integration layers
- **Examples**: `FacilitiesView.tsx`, `AuthContext.tsx`, custom hooks

#### 💼 Business Layer  
- **Purpose**: Core business logic, entities, and use cases
- **Contents**: Services, entities, business rules, domain models
- **Dependencies**: Should be framework-agnostic, minimal external dependencies
- **Examples**: `auth.service.ts`, `facility.service.ts`, business types

#### 🔌 Integration Layer
- **Purpose**: External service integration, APIs, data persistence
- **Contents**: API clients, database adapters, external service wrappers
- **Dependencies**: Can use external libraries (Axios, storage, etc.)
- **Examples**: API implementations, dependency injection registration

#### 🎯 Capabilities Layer
- **Purpose**: Domain contracts, types, and interfaces
- **Contents**: TypeScript interfaces, types, constants, domain contracts
- **Dependencies**: Pure TypeScript types, no runtime dependencies
- **Examples**: `facility-response.ts`, domain symbols, use case interfaces

## 🔄 Architecture Patterns & Practices

### Dependency Injection
- **Container**: Inversify IoC container
- **Registration**: Domain-specific DI registration files
- **Usage**: Custom hook `useDependencyInjection` for clean component integration

### State Management Strategy
- **Server State**: React Query for API data, caching, and synchronization
- **Client State**: Zustand for global app state
- **Local State**: React hooks (useState, useReducer) for component state
- **Secure Storage**: Expo Secure Store for authentication tokens

### Import Strategy
```typescript
// ✅ Preferred: Direct imports
import { FacilityList } from '../components/facility-list';
import { useSession } from '@/src/authentication/presentation/context/AuthContext';

// ❌ Avoid: Barrel exports (index.ts re-exports)
// Can cause circular dependencies and require cycle warnings
```

### Error Handling
- **API Errors**: Handled at service layer with proper typing
- **UI Errors**: Error boundaries and user-friendly error states
- **Network**: Automatic retry mechanisms with React Query
- **Authentication**: Secure token management with automatic logout

### Code Quality Standards
- **TypeScript**: Strict mode with experimental decorators
- **Linting**: ESLint with TypeScript rules
- **Formatting**: Prettier with consistent configuration
- **Commits**: Conventional commits with Husky pre-commit hooks

## 🎨 UI/UX Design Patterns

### Typography
- **Primary**: Inter (400, 500, 600, 700, 900)
- **Secondary**: Poppins (500, 600, 700, 800, 900)  
- **Accent**: DM Sans (400, 500, 700)

### Layout Patterns
- **Drawer Navigation**: Left-side drawer with custom content
- **Responsive Design**: Tablet-optimized layouts
- **Search Patterns**: Consistent search forms across domains
- **Data Display**: Reusable table and card components

## 🚧 Development Workflow

### Git Workflow & Conventional Commits

#### Making Commits
```bash
# Option 1: Interactive (Recommended)
npm run commit

# Option 2: Manual
git commit -m "feat(facilities): add search functionality"
```

#### Commit Types
- `feat`: New features
- `fix`: Bug fixes  
- `docs`: Documentation
- `style`: Formatting (no logic changes)
- `refactor`: Code restructuring
- `perf`: Performance improvements
- `test`: Adding/updating tests
- `chore`: Maintenance tasks
- `ci`: CI/CD changes
- `build`: Build system changes

#### Pre-commit Checks (Automatic)
- TypeScript type checking
- ESLint code quality checks  
- Prettier code formatting
- Commit message validation

### Adding New Features

#### 1. Create New Domain
```bash
src/new-domain/
├── business/
├── integration/  
├── presentation/
└── capabilities/
```

#### 2. Implement DDD Layers
1. Define types in `capabilities/`
2. Create business services in `business/`
3. Implement API integration in `integration/`
4. Build UI components in `presentation/`

#### 3. Register Dependencies
```typescript
// integration/register-dependency-injection.ts
container.bind(TYPES.NewDomainService).to(NewDomainService);
```

## 🧪 Testing Strategy

### Current Testing Setup
- **Framework**: Jest with React Native preset
- **Component Testing**: React Native Testing Library (to be implemented)
- **Unit Testing**: Business logic and utility functions

### Testing Commands
```bash
npm test              # Run all tests
npm test -- --watch  # Watch mode
```

## 🔐 Security & Best Practices

### Authentication Security
- Secure token storage with Expo Secure Store
- Automatic token expiration handling
- Network security configuration for API communication

### API Security
- HTTPS enforcement for all network calls
- Proper error handling without exposing sensitive information
- Network security config for staging/production environments

### Data Protection
- Input validation at domain boundaries
- Secure storage for sensitive user data
- Network monitoring and offline capability

## 🚀 Deployment & Build

### EAS Build Configuration
- **Project ID**: 46931528-1528-4e5f-a5cd-fdc4176f308b
- **Owner**: luis.moxie
- **Platform Support**: iOS, Android, Web

### Build Commands
```bash
# Development build
eas build --profile development

# Production build  
eas build --profile production

# Submit to app stores
eas submit
```

## 🐛 Troubleshooting

### Common Issues

#### Metro Bundle Issues
```bash
npx expo start --clear
```

#### Dependency Issues
```bash
rm -rf node_modules package-lock.json
npm install
```

#### iOS Simulator Issues
```bash
npx expo run:ios --device
```

#### Android Emulator Issues
```bash
npx expo run:android --device
```

### Pre-commit Hook Failures
1. Fix TypeScript errors: `npm run type-check`
2. Fix linting issues: `npm run lint:fix`  
3. Format code: `npm run format`
4. Retry commit

## 📚 Additional Resources

### Documentation
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Domain-Driven Design Guide](https://martinfowler.com/bliki/DomainDrivenDesign.html)

### Project Documentation
- [Contributing Guidelines](./CONTRIBUTING.md)
- [Commit Style Guide](./docs/commit-style.md)

## 🤝 Contributing

Please read [CONTRIBUTING.md](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is private and proprietary to MoxieLink.

---

**RPM App** - Built with ❤️ using React Native, Expo, and TypeScript