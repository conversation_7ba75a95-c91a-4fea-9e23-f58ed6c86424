import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, View } from 'react-native';

const radius = 80;

export const BluetoothRadar = () => {
  // Animation values for pulse rings
  const pulse1 = useRef(new Animated.Value(0)).current;
  const pulse2 = useRef(new Animated.Value(0)).current;
  const pulse3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Create staggered pulse animations
    const createPulseAnimation = (animatedValue: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      );
    };

    // Start staggered pulse animations
    const pulseAnimation1 = createPulseAnimation(pulse1, 0);
    const pulseAnimation2 = createPulseAnimation(pulse2, 700);
    const pulseAnimation3 = createPulseAnimation(pulse3, 1400);

    pulseAnimation1.start();
    pulseAnimation2.start();
    pulseAnimation3.start();

    // Cleanup
    return () => {
      pulseAnimation1.stop();
      pulseAnimation2.stop();
      pulseAnimation3.stop();
    };
  }, []);

  // Interpolate scale and opacity for each pulse ring
  const getPulseStyle = (animatedValue: Animated.Value) => ({
    transform: [
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.3, 1.2],
        }),
      },
    ],
    opacity: animatedValue.interpolate({
      inputRange: [0, 0.3, 1],
      outputRange: [0.8, 0.4, 0],
    }),
  });

  return (
    <View style={styles.container}>
      {/* Radar Container */}
      <View style={styles.radarContainer}>
        {/* Animated pulse rings */}
        <Animated.View style={[styles.pulseRing, getPulseStyle(pulse1)]} />
        <Animated.View style={[styles.pulseRing, getPulseStyle(pulse2)]} />
        <Animated.View style={[styles.pulseRing, getPulseStyle(pulse3)]} />

        {/* Main radar circle */}
        <View style={styles.mainCircle}>
          <Ionicons name="bluetooth" size={24} color="white" />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    color: '#666',
    marginBottom: 40,
    textAlign: 'center',
  },
  radarContainer: {
    width: 320,
    height: 320,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  pulseRing: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: '#4A90E2',
    backgroundColor: 'transparent',
  },
  mainCircle: {
    width: radius - 40,
    height: radius - 40,
    borderRadius: (radius - 40) / 2,
    backgroundColor: '#4A90E2',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    zIndex: 10,
  },
});
