import { Redirect, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { ImageBackground } from 'react-native';
import { useSession } from '../../src/authentication/presentation/context/AuthContext';

export default function AuthLayout() {
  const { session } = useSession();
  if (<PERSON><PERSON><PERSON>(session)) {
    return <Redirect href="/(app)" />;
  }
  return (
    <ImageBackground
      source={require('../../assets/images/background.png')}
      style={{ flex: 1 }}
      resizeMode="cover"
    >
      <StatusBar style="auto" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: {
            backgroundColor: 'transparent',
          },
        }}
      >
        <Stack.Screen
          name="login"
          options={{
            title: 'Login',
          }}
        />
      </Stack>
    </ImageBackground>
  );
}
