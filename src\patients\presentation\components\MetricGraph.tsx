import { COLORS } from '@/src/shared/business/constants/colors';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { LineChart, lineDataItem } from 'react-native-gifted-charts';

export interface ReadingData {
  value: number;
  time: string;
}

interface MetricGraphProps {
  max: number;
  min: number;
  data?: ReadingData[];
  color?: string;
  status?: 'normal' | 'high' | 'low';
}

export const MetricGraph: React.FC<MetricGraphProps> = ({
  max,
  min,
  data,
  color,
  status = 'normal',
}) => {
  // Mock data if none provided
  const mockData: ReadingData[] = [
    { value: 98, time: '09:00' },
    { value: 102, time: '10:00' },
    { value: 95, time: '11:00' },
    { value: 99, time: '12:00' },
    { value: 105, time: '13:00' },
    { value: 97, time: '14:00' },
    { value: 101, time: '15:00' },
    { value: 94, time: '16:00' },
    { value: 103, time: '17:00' },
    { value: 98, time: '18:00' },
  ];

  const chartData = data || mockData;

  // Dynamic color based on status
  const getStatusColor = (): string => {
    if (color) return color;

    switch (status) {
      case 'high':
        return '#EF4444'; // Red
      case 'low':
        return '#F59E0B'; // Orange/Yellow
      case 'normal':
      default:
        return COLORS.GREEN;
    }
  };

  const chartColor = getStatusColor();

  // Prepare data for LineChart
  const chartLineData: lineDataItem[] = chartData.map((point, index) => ({
    value: point.value,
    label: index % 2 === 0 ? point.time : '', // Show every other label to avoid crowding
    labelTextStyle: {
      color: COLORS.SUB_TEXT,
      fontSize: 7,
      fontFamily: 'DMSans-Regular',
      fontWeight: '400',
    },
  })); // 10% padding

  const chartHeight = 90;

  return (
    <View style={styles.container}>
      <View style={styles.chartWrapper}>
        <LineChart
          data={chartLineData}
          height={chartHeight}
          color1={chartColor}
          thickness1={2}
          curved
          areaChart
          initialSpacing={5}
          spacing={30}
          stripOverDataPoints
          endSpacing={3}
          noOfSections={3}
          yAxisColor="transparent"
          xAxisColor="transparent"
          yAxisThickness={0}
          xAxisThickness={0}
          rulesColor="#E5E7EB"
          rulesThickness={1}
          yAxisTextStyle={{ color: COLORS.SUB_TEXT, fontSize: 8 }}
          xAxisLabelTextStyle={{ color: COLORS.GREEN, fontSize: 1 }}
          yAxisLabelTexts={['40', '80', '100', '150']}
          hideDataPoints1={false}
          dataPointsColor1={chartColor}
          dataPointsRadius={2}
          focusEnabled={false}
          animateOnDataChange={true}
          animationDuration={300}
          startFillColor1={chartColor}
          endFillColor1={`${chartColor}10`}
          startOpacity={0.3}
          endOpacity={0.05}
          yAxisOffset={40}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingVertical: 8,
  },
  chartWrapper: {
    alignItems: 'center',
    marginBottom: 8,
    overflow: 'hidden',
    width: '100%',
  },
  chartInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  colorIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  infoText: {
    fontSize: 9,
    color: COLORS.SUB_TEXT,
    fontWeight: '500',
  },
  rangeText: {
    fontSize: 9,
    color: COLORS.SUB_TEXT,
    textTransform: 'capitalize',
  },
});
