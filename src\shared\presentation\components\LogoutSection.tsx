import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface LogoutSectionProps {
  onLogout?: () => void;
}

export default function LogoutSection({ onLogout }: LogoutSectionProps) {
  return (
    <View style={styles.logoutContainer}>
      <TouchableOpacity style={styles.logoutButton} onPress={onLogout}>
        <Text style={styles.logoutText}>Log out</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  logoutContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  logoutButton: {
    padding: 12,
    alignItems: 'center',
  },
  logoutText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
});
