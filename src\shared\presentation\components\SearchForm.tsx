import { Ionicons } from '@expo/vector-icons';
import React, { memo, useCallback, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { StyleSheet, TextInput, View } from 'react-native';
import { COLORS } from '../../business/constants/colors';

interface Props {
  onSearch?: (value: string) => void;
  placeholder?: string;
  debounceDelay?: number;
  initialValue?: string;
  containerStyle?: object;
  searchContainerStyle?: object;
  inputStyle?: object;
  iconColor?: string;
  iconSize?: number;
  backgroundColor?: string;
  placeholderTextColor?: string;
}

const SearchFormComponent: React.FC<Props> = ({
  onSearch,
  placeholder = 'Search',
  debounceDelay = 500,
  initialValue = '',
  containerStyle,
  searchContainerStyle,
  inputStyle,
  iconColor = COLORS.TEXT_MAIN,
  iconSize = 20,
  backgroundColor = COLORS.LIGHT_WHITE,
  placeholderTextColor = COLORS.SUB_TEXT,
}) => {
  const { control, watch } = useForm({
    defaultValues: {
      search: initialValue,
    },
  });

  const searchValue = watch('search');

  const debouncedSearch = useCallback(
    (() => {
      let timeoutId: number;
      return (value: string) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          onSearch?.(value);
        }, debounceDelay);
      };
    })(),
    [onSearch, debounceDelay]
  );

  useEffect(() => {
    if (searchValue !== undefined) {
      debouncedSearch(searchValue);
    }
  }, [searchValue, debouncedSearch]);

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={[styles.searchContainer, { backgroundColor }, searchContainerStyle]}>
        <Ionicons name="search" size={iconSize} color={iconColor} style={styles.searchIcon} />
        <Controller
          control={control}
          name="search"
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              style={[styles.searchInput, inputStyle]}
              placeholder={placeholder}
              autoCapitalize="none"
              autoCorrect={false}
              placeholderTextColor={placeholderTextColor}
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
            />
          )}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 999,
    height: 44,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 11,
    color: COLORS.TEXT_MAIN,
    height: '100%',
    fontFamily: 'DMSans-Regular',
    fontWeight: '300',
  },
});

export const SearchForm = memo(SearchFormComponent);
