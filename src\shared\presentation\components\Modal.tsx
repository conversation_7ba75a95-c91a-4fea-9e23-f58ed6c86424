import { COLORS } from '@/src/shared/business/constants/colors';
import { PropsWithChildren } from 'react';
import { Modal as RNModal, StyleSheet, View } from 'react-native';

interface Props extends PropsWithChildren {
  isVisible: boolean;
  onClose: () => void;
  animationType?: 'slide' | 'fade';
  width?: number;
}
export const Modal = ({
  isVisible,
  onClose,
  children,
  animationType = 'fade',
  width = 200,
}: Props) => {
  return (
    <RNModal
      animationType={animationType}
      visible={isVisible}
      onRequestClose={onClose}
      transparent={true}
    >
      <View style={styles.modalBackground}>
        <View style={[styles.modalContent, { width }]}>{children}</View>
      </View>
    </RNModal>
  );
};

const styles = StyleSheet.create({
  modal: {
    backgroundColor: COLORS.LIGHT_WHITE,
    padding: 16,
    borderRadius: 16,
    width: 100,
  },

  modalBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  modalContent: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: COLORS.WHITE,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    gap: 16,
  },
});
