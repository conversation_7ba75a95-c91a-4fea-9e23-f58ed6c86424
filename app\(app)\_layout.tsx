import { Ionicons } from '@expo/vector-icons';
import { Redirect, useNavigation } from 'expo-router';
import { Drawer } from 'expo-router/drawer';
import { TouchableOpacity } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { useSession } from '@/src/authentication/presentation/context/AuthContext';
import CustomDrawerContent from '@/src/shared/presentation/components/CustomDrawerContent';

export default function AppLayout() {
  const { session } = useSession();

  // Preload lazy components for better performance
  // useAutoPreload();

  if (!<PERSON><PERSON>an(session)) {
    return <Redirect href="/login" />;
  }
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        drawerContent={CustomDrawerContent}
        screenOptions={{
          headerShown: true,
          drawerType: 'slide',
          drawerHideStatusBarOnOpen: false,
          drawerPosition: 'left',
          drawerStyle: {
            width: 200,
            backgroundColor: 'transparent',
          },
          overlayColor: 'rgba(0, 0, 0, 0.5)',
          headerStyle: {
            backgroundColor: 'transparent',
            elevation: 0,
            shadowOpacity: 0,
          },
          headerTitleStyle: {
            fontWeight: '600',
            fontSize: 18,
          },
          headerLeft: () => {
            const navigation = useNavigation();
            return (
              <TouchableOpacity
                style={{ marginLeft: 16 }}
                onPress={() => (navigation as any).openDrawer()}
              >
                {/* TODO: replace with custom icon */}
                <Ionicons name="menu" size={24} color="#333" />
              </TouchableOpacity>
            );
          },
          sceneStyle: {
            backgroundColor: 'transparent',
          },
        }}
      >
        <Drawer.Screen
          name="dashboard"
          options={{
            drawerLabel: 'Dashboard',
            title: 'Dashboard',
          }}
        />
        <Drawer.Screen
          name="facilities"
          options={{
            drawerLabel: 'Facilities',
            title: 'Facilities',
          }}
        />
        <Drawer.Screen
          name="reporting"
          options={{
            drawerLabel: 'Reporting',
            title: 'Reporting',
            headerTitle: 'Reporting',
          }}
        />
        <Drawer.Screen
          name="(patients)"
          options={{
            drawerItemStyle: { display: 'none' },
            headerShown: false,
          }}
        />
      </Drawer>
    </GestureHandlerRootView>
  );
}
