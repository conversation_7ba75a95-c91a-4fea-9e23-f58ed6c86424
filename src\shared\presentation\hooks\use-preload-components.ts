import { useCallback, useEffect, useRef } from 'react';

interface PreloadOptions {
  delay?: number;
  onUserInteraction?: boolean;
  onIdle?: boolean;
}

// Hook para precargar componentes lazy
export const usePreloadComponents = () => {
  const preloadedComponents = useRef(new Set<string>());

  const preloadComponent = useCallback(
    async (
      componentName: string,
      importFunction: () => Promise<any>,
      options: PreloadOptions = {}
    ) => {
      // Evitar precargar el mismo componente múltiples veces
      if (preloadedComponents.current.has(componentName)) {
        return;
      }

      const { delay = 0, onUserInteraction = false, onIdle = false } = options;

      const doPreload = async () => {
        try {
          console.log(`Preloading component: ${componentName}`);
          await importFunction();
          preloadedComponents.current.add(componentName);
          console.log(`Successfully preloaded: ${componentName}`);
        } catch (error) {
          console.warn(`Failed to preload component ${componentName}:`, error);
        }
      };

      if (onUserInteraction) {
        const handleFirstInteraction = () => {
          setTimeout(doPreload, delay);
          // Remover listeners después de la primera interacción
          window.removeEventListener('click', handleFirstInteraction);
          window.removeEventListener('touchstart', handleFirstInteraction);
          window.removeEventListener('scroll', handleFirstInteraction);
        };

        window.addEventListener('click', handleFirstInteraction, { once: true });
        window.addEventListener('touchstart', handleFirstInteraction, { once: true });
        window.addEventListener('scroll', handleFirstInteraction, { once: true });
      } else if (onIdle) {
        if ('requestIdleCallback' in window) {
          (window as any).requestIdleCallback(() => {
            setTimeout(doPreload, delay);
          });
        } else {
          setTimeout(() => {
            setTimeout(doPreload, delay);
          }, 2000);
        }
      } else {
        setTimeout(doPreload, delay);
      }
    },
    []
  );

  const preloadBluetoothRadar = useCallback(() => {
    return preloadComponent('BluetoothRadar', () => import('../components/BluetoothRadar'), {
      onUserInteraction: true,
    });
  }, [preloadComponent]);

  const preloadReportingView = useCallback(() => {
    return preloadComponent(
      'ReportingView',
      () => import('../../../reporting/presentation/screens/ReportingView'),
      { delay: 3000, onIdle: true }
    );
  }, [preloadComponent]);

  const preloadSinglePatientView = useCallback(() => {
    return preloadComponent(
      'SinglePatientView',
      () => import('../../../patients/presentation/screens/SinglePatientView'),
      { delay: 2000, onIdle: true }
    );
  }, [preloadComponent]);

  return {
    preloadComponent,
    preloadBluetoothRadar,
    preloadReportingView,
    preloadSinglePatientView,
    isPreloaded: (componentName: string) => preloadedComponents.current.has(componentName),
  };
};

export const useAutoPreload = () => {
  const { preloadBluetoothRadar, preloadReportingView, preloadSinglePatientView } =
    usePreloadComponents();

  useEffect(() => {
    preloadBluetoothRadar();
    preloadReportingView();
    preloadSinglePatientView();
  }, [preloadBluetoothRadar, preloadReportingView, preloadSinglePatientView]);
};
