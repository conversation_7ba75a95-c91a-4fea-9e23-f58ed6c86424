# Contributing to RPM App

## Development Workflow

This project uses strict code quality standards and conventional commits to maintain a clean, maintainable codebase.

## Commit Message Convention

We use [Conventional Commits](https://www.conventionalcommits.org/) for our commit messages. This helps us:

- Generate changelogs automatically
- Determine semantic version bumps
- Keep a clean commit history

### Commit Message Format

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Commit Types

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **chore**: Changes to the build process or auxiliary tools and libraries
- **ci**: Changes to CI configuration files and scripts
- **build**: Changes that affect the build system or external dependencies
- **revert**: Reverts a previous commit

### Examples

```bash
feat(dashboard): add facility summary table
fix(navigation): resolve drawer closing issue
docs: update installation instructions
style: format code with prettier
refactor(api): extract user service
```

## Making Commits

### Option 1: Using Commitizen (Recommended)

```bash
npm run commit
```

This will start an interactive prompt to help you create a conventional commit.

### Option 2: Manual Commit

```bash
git add .
git commit -m "feat(dashboard): add new analytics widget"
```

## Pre-commit Hooks

Before each commit, the following checks run automatically:

- **Type checking**: Ensures TypeScript types are valid
- **Linting**: Checks code quality with ESLint
- **Formatting**: Verifies code is properly formatted with Prettier

If any check fails, the commit will be rejected. Fix the issues and try again.

## Development Commands

```bash
# Start development server
npm start

# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix

# Code formatting
npm run format
npm run format:check

# Interactive commit
npm run commit
```

## Code Quality Standards

- Follow the DDD (Domain-Driven Design) architecture
- Use TypeScript for all files
- Write descriptive variable and function names
- Add proper type definitions
- Follow the project's ESLint and Prettier configurations
- Write tests for business logic

## Pull Request Process

1. Create a feature branch from `main`
2. Make your changes following the coding standards
3. Ensure all pre-commit hooks pass
4. Create a pull request with a descriptive title
5. Ensure CI/CD checks pass
6. Request code review from team members

## Troubleshooting

### Pre-commit hooks failing

If pre-commit hooks fail:

1. Run `npm run type-check` to fix TypeScript errors
2. Run `npm run lint:fix` to auto-fix linting issues
3. Run `npm run format` to format your code
4. Try committing again

### Commitlint failing

Ensure your commit message follows the conventional format:

- Use lowercase for type and scope
- Don't end the subject with a period
- Keep the subject line under 100 characters
