import { Image } from 'expo-image';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

interface AlertItemProps {
  title: string;
  subtitle: string;
  value: string;
}

export const AlertItem: React.FC<AlertItemProps> = ({ title, subtitle, value }) => {
  return (
    <View style={styles.container}>
      <View style={styles.alertContent}>
        <Image
          source={require('../../../../assets/icons/facility-alert.svg')}
          style={styles.icon}
        />
        <View style={styles.alertContentText}>
          <Text style={styles.alertContentTextTitle}>{title}</Text>
          <Text style={styles.alertContentTextSubtitle}>{subtitle}</Text>
        </View>
        <View>
          <Text style={styles.alertContentTextValue}>{value}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 12,
    backgroundColor: 'white',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  alertContent: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  icon: {
    width: 34,
    height: 34,
  },
  alertContentText: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
  },
  alertContentTextTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#232223',
    fontFamily: 'DMSans-Medium',
  },
  alertContentTextSubtitle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#8B939A',
    fontFamily: 'DMSans',
  },
  alertContentTextValue: {
    fontSize: 28,
    fontWeight: '700',
    color: '#232223',
    fontFamily: 'DMSans',
  },
});
