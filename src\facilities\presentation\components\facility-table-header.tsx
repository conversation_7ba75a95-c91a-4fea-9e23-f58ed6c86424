import { SCREEN_SIZE } from '@/src/shared/business/constants/screen-size';
import React, { memo } from 'react';
import { Dimensions, StyleSheet, Text, View } from 'react-native';

const screenWidth = Dimensions.get('window').width;
const isSmallScreen = screenWidth < SCREEN_SIZE.SMALL;

const FacilityTableHeaderComponent = () => {
  const renderName = isSmallScreen ? 'Name - Data' : 'FACILITY NAME';
  return (
    <View style={[styles.row, styles.headerRow]}>
      <Text style={[styles.cell, styles.headerCell, styles.srNoColumn]}>SR. NO.</Text>
      <Text style={[styles.cell, styles.headerCell, styles.nameColumn]}>{renderName}</Text>
      {!isSmallScreen && (
        <>
          <Text style={[styles.cell, styles.headerCell, styles.numberColumn]}>NO. OF PATIENTS</Text>
          <Text style={[styles.cell, styles.headerCell, styles.numberColumn]}>NO. OF DEVICES</Text>
          <Text style={[styles.cell, styles.headerCell, styles.numberColumn]}>ALERTS</Text>
          <Text style={[styles.cell, styles.headerCell, styles.addressColumn]}>ADDRESS</Text>
        </>
      )}
      <View style={[styles.cell, styles.chevronColumn]} />
    </View>
  );
};

const styles = StyleSheet.create({
  headerRow: {
    backgroundColor: '#FAFAFA',
  },
  cell: {
    paddingHorizontal: 8,
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
    minHeight: 60,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  headerCell: {
    fontWeight: '600',
    fontSize: 11,
    color: '#2B3674',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    // textAlign: 'center',
  },
  addressColumn: {
    flex: 1.5,
    minWidth: 120,
  },
  srNoColumn: {
    width: 60,
    textAlign: 'center',
  },
  nameColumn: {
    flex: 2,
    width: 100,
  },
  numberColumn: {
    width: 100,
    textAlign: 'center',
  },
  chevronColumn: {
    width: 40,
    alignItems: 'center',
  },
});

export const FacilityTableHeader = memo(FacilityTableHeaderComponent);
