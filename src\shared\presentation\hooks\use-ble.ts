import {
  DEVICE_CHARACTERISTICS,
  DEVICE_SERVICE_UUIDS,
} from '@/src/shared/business/constants/ble-constants';
import {
  ConnectedDeviceInfo,
  DeviceType,
  HealthData,
  MeasurementType,
} from '@/src/shared/business/types/ble-types';
import * as ExpoDevice from 'expo-device';
import { useCallback, useMemo, useState } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import base64 from 'react-native-base64';
import { BleError, BleManager, Characteristic, Device } from 'react-native-ble-plx';
import { useDeviceStore } from '../store/devivce-store';

export const useBLE = () => {
  const bleManager = useMemo(() => new BleManager(), []);
  const { setActiveDevice, activeDevice, resetActiveDevice } = useDeviceStore();
  const [allDevices, setAllDevices] = useState<Device[]>([]);
  const [connectedDeviceInfo, setConnectedDeviceInfo] = useState<ConnectedDeviceInfo | null>(null);
  const [healthData, setHealthData] = useState<HealthData | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isMeasuring, setIsMeasuring] = useState(false);
  const [measurementType, setMeasurementType] = useState<MeasurementType | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  // Device type identification based on service UUIDs
  const identifyDeviceType = useCallback(async (device: Device): Promise<DeviceType> => {
    try {
      const services = await device.services();
      console.log(
        'Available services:',
        services.map((s) => s.uuid)
      );

      for (const service of services) {
        const serviceUUID = service.uuid.toLowerCase();
        console.log('Checking service:', serviceUUID);

        if (serviceUUID.includes(DEVICE_SERVICE_UUIDS.ring.toLowerCase())) {
          console.log('Device identified as ring');
          return 'ring';
        }
        if (serviceUUID.includes(DEVICE_SERVICE_UUIDS.oximeter.toLowerCase())) {
          console.log('Device identified as oximeter');
          return 'oximeter';
        }
      }

      console.log('Device type unknown - no matching services found');
      return 'unknown';
    } catch (error) {
      console.error('Failed to identify device type:', error);
      return 'unknown';
    }
  }, []);

  // Convert Uint8Array to base64 for BLE transmission
  const toBase64 = useCallback((data: Uint8Array): string => {
    return base64.encodeFromByteArray(data);
  }, []);

  // Convert base64 to byte array
  const fromBase64 = useCallback((data: string): number[] => {
    const binaryString = base64.decode(data);
    const bytes: number[] = [];
    for (let i = 0; i < binaryString.length; i++) {
      bytes.push(binaryString.charCodeAt(i));
    }
    return bytes;
  }, []);

  // Parse health data based on device type
  const parseHealthData = useCallback(
    (data: string, deviceType: DeviceType): HealthData | null => {
      try {
        const bytes = fromBase64(data);

        if (deviceType === 'oximeter') {
          return parseOximeterData(bytes);
        } else if (deviceType === 'ring') {
          return parseRingData(bytes);
        }

        return null;
      } catch (error) {
        console.error('Error parsing health data:', error);
        return null;
      }
    },
    [fromBase64]
  );

  // Oximeter data parsing (SPO2 and Pulse Rate)
  const parseOximeterData = useCallback((bytes: number[]): HealthData | null => {
    if (bytes.length < 2) return null;

    const command = bytes[0];

    // SPO2 and PR data command (0x81)
    if (command === 0x81 && bytes.length >= 4) {
      return {
        spo2: bytes[1], // SpO2 percentage
        heartRate: bytes[2], // Pulse rate
        timestamp: new Date(),
        deviceType: 'oximeter',
        rawData: bytes,
      };
    }

    return null;
  }, []);

  // Ring data parsing (real-time step counting response from working use-jstyle-ring logic)
  const parseRingData = useCallback((bytes: number[]): HealthData | null => {
    if (bytes.length < 2) return null;

    const command = bytes[0];

    // Real-time step counting response (0x09) - this is what actually works!
    if (command === 0x09) {
      console.log('Real-time step counting data received - command 0x09 (working command)');

      // Extract heart rate from the data if available
      let heartRate = 0;
      if (bytes.length >= 22) {
        // Heart rate is typically in bytes[21] or similar position
        heartRate = bytes[21];
      }

      return {
        heartRate: heartRate > 0 ? heartRate : undefined,
        spo2: undefined, // Real-time step counting doesn't provide SpO2
        timestamp: new Date(),
        deviceType: 'ring',
        rawData: bytes,
      };
    }

    // MeasurementWithType response (0x86) - keep as backup
    if (command === 0x86 && bytes.length >= 8) {
      return {
        heartRate: bytes[2],
        spo2: bytes[3],
        timestamp: new Date(),
        deviceType: 'ring',
        hrv: bytes[4],
        fatigue: bytes[5],
        rawData: bytes,
      };
    }

    return null;
  }, []);

  // Request Android permissions
  const requestAndroid31Permissions = async () => {
    const bluetoothScanPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
      {
        title: 'Bluetooth Scan Permission',
        message: 'This app needs Bluetooth scan permission to discover devices',
        buttonPositive: 'OK',
      }
    );
    const bluetoothConnectPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
      {
        title: 'Bluetooth Connect Permission',
        message: 'This app needs Bluetooth connect permission to connect to devices',
        buttonPositive: 'OK',
      }
    );
    const fineLocationPermission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: 'Location Permission',
        message: 'Bluetooth Low Energy requires Location permission to scan for devices',
        buttonPositive: 'OK',
      }
    );

    const allGranted =
      bluetoothScanPermission === 'granted' &&
      bluetoothConnectPermission === 'granted' &&
      fineLocationPermission === 'granted';

    return {
      granted: allGranted,
      message: !allGranted ? 'One or more Bluetooth permissions were denied' : undefined,
    };
  };

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      if ((ExpoDevice.platformApiLevel ?? -1) < 31) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'Bluetooth Low Energy requires Location permission to scan for devices',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        const result = await requestAndroid31Permissions();
        return result.granted;
      }
    } else {
      return true;
    }
  };

  const isDuplicateDevice = (devices: Device[], nextDevice: Device) =>
    devices.findIndex((device) => nextDevice.id === device.id) > -1;

  const stopScan = useCallback(() => {
    bleManager.stopDeviceScan();
    setIsScanning(false);
  }, [bleManager]);

  const scanForDevices = useCallback(() => {
    if (isScanning) return;

    setIsScanning(true);
    setAllDevices([]);

    bleManager.startDeviceScan(null, null, (error, device) => {
      if (error) {
        console.error('Scan error:', error);
        setIsScanning(false);
        return;
      }

      if (device && device.name) {
        console.log('found device', device.name);
        setAllDevices((prevState: Device[]) => {
          if (!isDuplicateDevice(prevState, device)) {
            return [...prevState, device];
          }
          return prevState;
        });
      }
    });
  }, [bleManager, isScanning]);

  const connectToDevice = useCallback(
    async (device: Device) => {
      try {
        setIsConnecting(true);

        // Connect to device with timeout
        const deviceConnection = await bleManager.connectToDevice(device.id, {
          timeout: 10000, // 10 second timeout
        });

        console.log('Device connected, discovering services...');
        await deviceConnection.discoverAllServicesAndCharacteristics();

        // Identify device type
        const deviceType = await identifyDeviceType(deviceConnection);
        console.log('Device type identified:', deviceType);

        if (deviceType === 'unknown') {
          throw new Error('Unknown device type - device not supported');
        }

        if (deviceType === 'bracelet') {
          throw new Error('Bracelet device type not yet implemented');
        }

        // Get device-specific characteristics
        const characteristics = DEVICE_CHARACTERISTICS[deviceType];

        // Set connected device info
        const deviceInfo: ConnectedDeviceInfo = {
          device: deviceConnection,
          type: deviceType,
          characteristics,
        };

        setActiveDevice(deviceConnection);
        setConnectedDeviceInfo(deviceInfo);
        bleManager.stopDeviceScan();
        setIsScanning(false);

        // Add connection state listener
        deviceConnection.onDisconnected((error, disconnectedDevice) => {
          console.log('Device disconnected:', disconnectedDevice?.name, error);
          setConnectedDeviceInfo(null);
          setHealthData(null);
          setIsMeasuring(false);
          setMeasurementType(null);
        });

        // Wait a bit before starting monitoring to ensure connection is stable
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Start monitoring for health data
        startMonitoring(deviceInfo);

        console.log(`Connected to ${deviceType} device:`, device.name);
      } catch (error) {
        console.error('Failed to connect to device:', error);
        throw error;
      } finally {
        setIsConnecting(false);
      }
    },
    [bleManager, identifyDeviceType]
  );

  const startMonitoring = useCallback(
    (deviceInfo: ConnectedDeviceInfo) => {
      const { device, characteristics, type } = deviceInfo;

      console.log(`Starting monitoring for ${type} device...`);
      console.log('Service UUID:', characteristics.serviceUUID);
      console.log('RX UUID:', characteristics.rxUUID);

      try {
        device.monitorCharacteristicForService(
          characteristics.serviceUUID,
          characteristics.rxUUID,
          (error: BleError | null, characteristic: Characteristic | null) => {
            if (error) {
              console.error('Monitoring error:', error);
              // Don't disconnect on monitoring error, just log it
              return;
            }

            if (!characteristic?.value) {
              console.log('No data received from characteristic');
              return;
            }

            console.log('Received characteristic value:', characteristic.value);
            const parsedData = parseHealthData(characteristic.value, type);
            if (parsedData) {
              setHealthData(parsedData);
              console.log(`Received ${type} data:`, parsedData);
            }
          }
        );
        console.log('Monitoring started successfully');
      } catch (error) {
        console.error('Failed to start monitoring:', error);
      }
    },
    [parseHealthData]
  );

  const sendCommand = useCallback(
    async (command: Uint8Array) => {
      if (!connectedDeviceInfo) {
        throw new Error('No device connected');
      }

      const { device, characteristics } = connectedDeviceInfo;

      try {
        await device.writeCharacteristicWithResponseForService(
          characteristics.serviceUUID,
          characteristics.txUUID,
          toBase64(command)
        );
      } catch (error) {
        console.error('Failed to send command:', error);
        throw error;
      }
    },
    [connectedDeviceInfo, toBase64]
  );

  // Unified measurement start method - adapts based on device type
  const startMeasurement = useCallback(
    async (type: MeasurementType) => {
      if (!connectedDeviceInfo) {
        throw new Error('No device connected');
      }

      setIsMeasuring(true);
      setMeasurementType(type);

      const { type: deviceType } = connectedDeviceInfo;

      try {
        if (deviceType === 'oximeter') {
          await startOximeterMeasurement();
        } else if (deviceType === 'ring') {
          await startRingMeasurement();
        }

        console.log(`Started ${type} measurement on ${deviceType}`);
      } catch (error) {
        setIsMeasuring(false);
        setMeasurementType(null);
        throw error;
      }
    },
    [connectedDeviceInfo]
  );

  // Oximeter-specific measurement
  const startOximeterMeasurement = useCallback(async () => {
    // For oximeter, request SPO2 and PR data
    const command = new Uint8Array([0x81]); // SPO2_AND_PR_DATA command
    await sendCommand(command);
  }, [sendCommand]);

  // Ring-specific measurement (using working real-time step counting from use-jstyle-ring)
  const startRingMeasurement = useCallback(async () => {
    if (!connectedDeviceInfo) {
      throw new Error('No device connected');
    }

    const { device } = connectedDeviceInfo;

    // Find the smart ring service (same as working use-jstyle-ring logic)
    const services = await device.services();
    const smartRingService = services.find((service) =>
      service.uuid.toLowerCase().includes(DEVICE_SERVICE_UUIDS.ring)
    );

    if (!smartRingService) {
      throw new Error('J Style smart ring service not available on this device');
    }

    // Use the working real-time step counting command
    const command = new Uint8Array(16);
    command[0] = 0x09; // REAL_TIME_STEP_COUNTING command (this actually works!)
    command[1] = 0x01;
    command[2] = 0x01;

    // Calculate CRC (same as working use-jstyle-ring logic)
    let crc = 0;
    for (let i = 0; i < 15; i++) {
      crc += command[i];
    }
    command[15] = crc & 0xff;

    // Send using base64 encoding (same as working logic)
    const commandBase64 = base64.encode(String.fromCharCode(...command));

    await device.writeCharacteristicWithResponseForService(
      smartRingService.uuid,
      DEVICE_CHARACTERISTICS.ring.txUUID,
      commandBase64
    );

    console.log('Ring real-time step counting started successfully');
  }, [connectedDeviceInfo]);

  const stopMeasurement = useCallback(async () => {
    if (!connectedDeviceInfo || !measurementType) {
      return;
    }

    try {
      // Device-specific stop logic can be added here
      setIsMeasuring(false);
      setMeasurementType(null);
      console.log('Measurement stopped');
    } catch (error) {
      console.error('Failed to stop measurement:', error);
      throw error;
    }
  }, [connectedDeviceInfo, measurementType]);

  const disconnectFromDevice = useCallback(() => {
    console.log('Disconnecting from device:', connectedDeviceInfo?.device?.name);
    resetActiveDevice();
    if (connectedDeviceInfo) {
      try {
        bleManager.cancelDeviceConnection(connectedDeviceInfo.device.id);
        console.log('Device connection cancelled');
      } catch (error) {
        console.error('Error cancelling device connection:', error);
      }
      setConnectedDeviceInfo(null);
      setHealthData(null);
      setIsMeasuring(false);
      setMeasurementType(null);
    }
  }, [connectedDeviceInfo, bleManager, resetActiveDevice]);

  const checkConnection = async () => {
    if (connectedDeviceInfo) {
      return true;
    }
    if (!connectedDeviceInfo && activeDevice) {
      try {
        await connectToDevice(activeDevice);
        return true;
      } catch {
        resetActiveDevice();
        return false;
      }
    }
    return true;
  };

  return {
    // Device management
    scanForDevices,
    stopScan,
    connectToDevice,
    disconnectFromDevice,
    requestPermissions,
    checkConnection,

    // State
    allDevices,
    connectedDevice: connectedDeviceInfo?.device || null,
    deviceType: connectedDeviceInfo?.type || null,
    isScanning,
    isConnecting,

    // Measurements
    startMeasurement,
    stopMeasurement,
    healthData,
    isMeasuring,
    measurementType,
  };
};
