import { AUTH_DEPENDENCY_INJECTION_TYPES } from '../../../business/types/auth-symbols';

describe('Authentication Dependency Injection Types', () => {
  describe('AUTH_DEPENDENCY_INJECTION_TYPES', () => {
    it('should be defined as an object', () => {
      expect(AUTH_DEPENDENCY_INJECTION_TYPES).toBeDefined();
      expect(typeof AUTH_DEPENDENCY_INJECTION_TYPES).toBe('object');
      expect(AUTH_DEPENDENCY_INJECTION_TYPES).not.toBeNull();
    });

    it('should have AuthService symbol', () => {
      expect(AUTH_DEPENDENCY_INJECTION_TYPES.AuthService).toBeDefined();
      expect(typeof AUTH_DEPENDENCY_INJECTION_TYPES.AuthService).toBe('symbol');
    });

    it('should create unique symbols', () => {
      // Symbols should be unique
      const symbol1 = AUTH_DEPENDENCY_INJECTION_TYPES.AuthService;
      const symbol2 = Symbol.for('AuthService');

      // These should be equal because Symbol.for returns the same symbol for the same key
      expect(symbol1).toBe(symbol2);
    });

    it('should have symbol with correct description', () => {
      const symbolString = AUTH_DEPENDENCY_INJECTION_TYPES.AuthService.toString();
      expect(symbolString).toContain('AuthService');
    });

    it('should be readonly in TypeScript', () => {
      // The const assertion provides compile-time readonly protection
      // This test verifies the structure exists and is properly typed
      expect(AUTH_DEPENDENCY_INJECTION_TYPES).toHaveProperty('AuthService');
      expect(typeof AUTH_DEPENDENCY_INJECTION_TYPES.AuthService).toBe('symbol');
    });

    it('should have all required service types', () => {
      const requiredServices = ['AuthService'];

      requiredServices.forEach((service) => {
        expect(AUTH_DEPENDENCY_INJECTION_TYPES).toHaveProperty(service);
        expect(
          typeof AUTH_DEPENDENCY_INJECTION_TYPES[
            service as keyof typeof AUTH_DEPENDENCY_INJECTION_TYPES
          ]
        ).toBe('symbol');
      });
    });
  });

  describe('Symbol uniqueness and registry', () => {
    it('should maintain consistency across imports', () => {
      // Re-import to test consistency
      const {
        AUTH_DEPENDENCY_INJECTION_TYPES: reimported,
      } = require('../../../business/types/auth-symbols');

      expect(reimported.AuthService).toBe(AUTH_DEPENDENCY_INJECTION_TYPES.AuthService);
    });

    it('should work with Symbol.for registry', () => {
      // Test that the symbol can be retrieved from global registry
      const registrySymbol = Symbol.for('AuthService');
      expect(registrySymbol).toBe(AUTH_DEPENDENCY_INJECTION_TYPES.AuthService);
    });
  });
});
